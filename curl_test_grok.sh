export GROK_API_KEY=************************************************************************************

curl https://api.x.ai/v1/chat/completions \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $GROK_API_KEY" \
-d '{
    "messages": [
        {
            "role": "system",
            "content": "You are <PERSON><PERSON>, a highly intelligent, helpful AI assistant."
        },
        {
            "role": "user",
            "content": "What is the meaning of life, the universe, and everything?"
        }
    ],
    "model": "grok-3-mini-fast-beta",
    "stream": false,
    "temperature": 0
}'
