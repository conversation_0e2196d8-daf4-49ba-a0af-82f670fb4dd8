<!DOCTYPE html>
<html>
<head>
    <title>Test TipTop Extension Configuration</title>
</head>
<body>
    <h1>Test TipTop Extension Configuration</h1>
    <div id="results"></div>
    
    <script>
        async function testConfig() {
            const results = document.getElementById('results');
            
            // Test 1: Check if mode indicator file exists
            try {
                const response = await fetch('./tiptop-mode.json');
                if (response.ok) {
                    const modeData = await response.json();
                    results.innerHTML += `<p><strong>Mode Indicator File:</strong> Found - ${JSON.stringify(modeData)}</p>`;
                } else {
                    results.innerHTML += `<p><strong>Mode Indicator File:</strong> Not found (${response.status})</p>`;
                }
            } catch (error) {
                results.innerHTML += `<p><strong>Mode Indicator File:</strong> Error - ${error.message}</p>`;
            }
            
            // Test 2: Test API connectivity
            try {
                const apiResponse = await fetch('http://localhost:30080/tiptop', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ url: 'https://example.com' })
                });
                
                if (apiResponse.ok) {
                    const data = await apiResponse.json();
                    results.innerHTML += `<p><strong>API Test:</strong> Success - Got response with ${data.summary?.text?.length || 0} characters</p>`;
                } else {
                    results.innerHTML += `<p><strong>API Test:</strong> Failed - ${apiResponse.status} ${apiResponse.statusText}</p>`;
                }
            } catch (error) {
                results.innerHTML += `<p><strong>API Test:</strong> Error - ${error.message}</p>`;
            }
        }
        
        testConfig();
    </script>
</body>
</html>
