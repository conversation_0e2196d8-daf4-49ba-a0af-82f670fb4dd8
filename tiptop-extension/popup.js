// Popup script for TipTop extension

document.addEventListener('DOMContentLoaded', function() {
    const statusElement = document.getElementById('status');
    const testModeToggle = document.getElementById('test-mode-toggle');
    const mockDataToggle = document.getElementById('mock-data-toggle');
    const environmentBadge = document.getElementById('environment-badge');

    // Initialize the toggles based on current configuration
    function initializeTestModeToggle() {
        // Check if we have access to the TipTopConfig object
        if (window.TipTopConfig) {
            // Set test mode toggle
            testModeToggle.checked = window.TipTopConfig.isTestMode;

            // Set mock data toggle
            mockDataToggle.checked = window.TipTopConfig.features &&
                                    window.TipTopConfig.features.useMockData === true;

            // Update UI
            updateEnvironmentBadge(window.TipTopConfig.isTestMode, window.TipTopConfig.modeSource);

            // Add a status message showing the configuration
            statusElement.textContent = `Mode: ${window.TipTopConfig.isTestMode ? 'TEST' : 'PRODUCTION'}, ` +
                `Source: ${window.TipTopConfig.modeSource || 'unknown'}, ` +
                `Mock Data: ${mockDataToggle.checked ? 'ON' : 'OFF'}`;
        } else {
            // Fall back to chrome.storage.local if TipTopConfig is not available
            chrome.storage.local.get(['TIPTOP_TEST_MODE', 'TIPTOP_USE_MOCK_DATA'], function(result) {
                const isTestMode = result.TIPTOP_TEST_MODE === 'true';
                const useMockData = result.TIPTOP_USE_MOCK_DATA === 'true';

                testModeToggle.checked = isTestMode;
                mockDataToggle.checked = useMockData;

                updateEnvironmentBadge(isTestMode, 'storage');
                statusElement.textContent = `Mode: ${isTestMode ? 'TEST' : 'PRODUCTION'}, ` +
                    `Source: storage, Mock Data: ${useMockData ? 'ON' : 'OFF'}`;
            });
        }
    }

    // Update the environment badge based on test mode
    function updateEnvironmentBadge(isTestMode, modeSource) {
        let badgeText = isTestMode ? 'Test' : 'Production';

        // Add source indicator if available
        if (modeSource) {
            // Convert first letter to uppercase and add source in parentheses
            const sourceText = modeSource.charAt(0).toUpperCase() + modeSource.slice(1);
            badgeText += ` (${sourceText})`;
        }

        environmentBadge.textContent = badgeText;
        environmentBadge.className = 'environment-indicator ' +
            (isTestMode ? 'environment-test' : 'environment-production');
    }

    // Initialize the toggle
    initializeTestModeToggle();

    // Test mode toggle
    testModeToggle.addEventListener('change', function() {
        const isTestMode = testModeToggle.checked;
        statusElement.textContent = `Switching to ${isTestMode ? 'TEST' : 'PRODUCTION'} mode...`;

        // Update chrome.storage.local
        chrome.storage.local.set({ 'TIPTOP_TEST_MODE': isTestMode ? 'true' : 'false' });

        // Update the environment badge
        updateEnvironmentBadge(isTestMode, 'userToggle');

        // If we have access to the TipTopConfig object, use its toggle function
        if (window.TipTopConfig && window.TipTopConfig.toggleTestMode) {
            window.TipTopConfig.toggleTestMode(isTestMode);
        } else {
            // Otherwise, reload the extension
            statusElement.textContent = `${isTestMode ? 'TEST' : 'PRODUCTION'} mode enabled. Reloading extension...`;
            setTimeout(() => {
                chrome.runtime.reload();
            }, 1000);
        }
    });

    // Mock data toggle
    mockDataToggle.addEventListener('change', function() {
        const useMockData = mockDataToggle.checked;
        statusElement.textContent = `${useMockData ? 'Enabling' : 'Disabling'} mock data...`;

        // Update chrome.storage.local
        chrome.storage.local.set({ 'TIPTOP_USE_MOCK_DATA': useMockData ? 'true' : 'false' });

        // If we have access to the TipTopConfig object, update it directly
        if (window.TipTopConfig && window.TipTopConfig.features) {
            window.TipTopConfig.features.useMockData = useMockData;

            // Update the status message
            statusElement.textContent = `Mock data ${useMockData ? 'enabled' : 'disabled'}. ` +
                `Mode: ${window.TipTopConfig.isTestMode ? 'TEST' : 'PRODUCTION'}`;

            // Reload the extension to apply changes
            setTimeout(() => {
                chrome.runtime.reload();
            }, 1000);
        } else {
            // Otherwise, reload the extension
            statusElement.textContent = `Mock data ${useMockData ? 'enabled' : 'disabled'}. Reloading extension...`;
            setTimeout(() => {
                chrome.runtime.reload();
            }, 1000);
        }
    });

    // Test Icons button
    document.getElementById('test-icons').addEventListener('click', function() {
        statusElement.textContent = 'Running icon test...';

        chrome.runtime.sendMessage({type: 'TEST_ICONS'}, function(response) {
            if (response && response.success) {
                statusElement.textContent = 'Icon test running in the active tab. Check the page.';
            } else {
                statusElement.textContent = 'Error: ' + (response ? response.error : 'Unknown error');
            }
        });
    });

    // Open Test Page button
    document.getElementById('open-test-page').addEventListener('click', function() {
        statusElement.textContent = 'Opening test page...';

        // Get the extension URL for the test page
        const testPageUrl = chrome.runtime.getURL('icon-test.html');

        // Open the test page in a new tab
        chrome.tabs.create({url: testPageUrl}, function(tab) {
            statusElement.textContent = 'Test page opened in a new tab.';
        });
    });
});
