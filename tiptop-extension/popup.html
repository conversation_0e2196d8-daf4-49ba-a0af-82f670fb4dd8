<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TipTop Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            width: 300px;
            padding: 15px;
        }
        h1 {
            font-size: 18px;
            color: #3498db;
            margin-top: 0;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 10px;
            width: 100%;
        }
        button:hover {
            background-color: #2980b9;
        }
        .status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
        }
        .toggle-container {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        .toggle-label {
            flex-grow: 1;
            font-weight: bold;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .toggle-slider {
            background-color: #3498db;
        }
        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }
        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin-top: 15px;
            margin-bottom: 10px;
            color: #555;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 5px;
        }
        .environment-indicator {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 8px;
        }
        .environment-test {
            background-color: #ffeeba;
            color: #856404;
        }
        .environment-production {
            background-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>TipTop Extension <span id="environment-badge" class="environment-indicator environment-production">Production</span></h1>

    <div class="toggle-container">
        <span class="toggle-label">Test Mode</span>
        <label class="toggle-switch">
            <input type="checkbox" id="test-mode-toggle">
            <span class="toggle-slider"></span>
        </label>
    </div>

    <div class="toggle-container">
        <span class="toggle-label">Use Mock Data</span>
        <label class="toggle-switch">
            <input type="checkbox" id="mock-data-toggle">
            <span class="toggle-slider"></span>
        </label>
    </div>

    <div class="section-title">Development Tools</div>
    <button id="test-icons">Test Icons</button>
    <button id="open-test-page">Open Test Page</button>

    <div class="status" id="status"></div>

    <script src="config.js"></script>
    <script src="popup.js"></script>
</body>
</html>
