// Random meaningful default names for TipTop social features
const RANDOM_NAMES = [
  // Famous scientists
  "Curious Einstein",
  "Thoughtful Newton",
  "Analytical Curie",
  "Insightful Darwin",
  "Brilliant Tesla",
  "Observant Galileo",
  "Innovative Edison",
  "Visionary Hawking",
  "Methodical Bohr",
  "<PERSON><PERSON> Feynman",
  
  // Famous writers
  "Creative Austen",
  "Eloquent Shakespeare",
  "Imaginative Tolkien",
  "Expressive Hemingway",
  "Thoughtful Woolf",
  "Narrative Dickens",
  "Poetic Angelou",
  "Insightful Orwell",
  "Descriptive Fitzgerald",
  "Analytical Tolstoy",
  
  // Famous artists
  "Colorful Picasso",
  "Expressive Van Gogh",
  "Detailed Da Vinci",
  "Surreal Dali",
  "Innovative Warhol",
  "Impressionist Monet",
  "Visionary Michelangelo",
  "Creative Kahlo",
  "Expressive Pollock",
  "Geometric Mondrian",
  
  // Famous philosophers
  "Questioning Socrates",
  "Logical Aristotle",
  "Reflective Plato",
  "Analytical Kant",
  "Existential Nietzsche",
  "Methodical Descartes",
  "Prag<PERSON> James",
  "Dialectical Hegel",
  "Em<PERSON><PERSON> Locke",
  "Skeptical Hume",
  
  // Internet-themed names
  "Curious Browser",
  "Thoughtful Reader",
  "Digital Explorer",
  "Web Wanderer",
  "Knowledge Seeker",
  "Content Curator",
  "Page Surfer",
  "Link Follower",
  "Info Hunter",
  "Data Diver"
];

// Random avatar colors
const AVATAR_COLORS = [
  "#3498db", // Blue
  "#2ecc71", // Green
  "#e74c3c", // Red
  "#f39c12", // Orange
  "#9b59b6", // Purple
  "#1abc9c", // Turquoise
  "#d35400", // Pumpkin
  "#2980b9", // Dark Blue
  "#27ae60", // Dark Green
  "#c0392b", // Dark Red
  "#8e44ad", // Dark Purple
  "#16a085", // Dark Turquoise
];

// Function to get a random name
function getRandomName() {
  return RANDOM_NAMES[Math.floor(Math.random() * RANDOM_NAMES.length)];
}

// Function to get a random color
function getRandomColor() {
  return AVATAR_COLORS[Math.floor(Math.random() * AVATAR_COLORS.length)];
}

// Export functions for use in other modules
window.TipTopRandomNames = {
  getRandomName,
  getRandomColor
};
