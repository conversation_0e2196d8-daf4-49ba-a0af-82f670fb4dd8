// This script tests the icon generation for the TipTop extension
// It can be run in the browser console to verify the icon generation works

// Function to generate a consistent color based on domain name
function getRandomColor(domain, offset = 0) {
    if (!domain) return '#3498db';
    
    // Create a hash from the domain string
    let hash = 0;
    for (let i = 0; i < domain.length; i++) {
        hash = domain.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    // Add the offset to create a related but different color
    hash += offset;
    
    // Convert to a good-looking hex color
    const hue = ((hash % 360) + 360) % 360; // 0-359 degrees
    const saturation = 70 + (hash % 20); // 70-89%
    const lightness = 45 + (hash % 15); // 45-59%
    
    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
}

// Test domains
const testDomains = [
    'google.com',
    'github.com',
    'stackoverflow.com',
    'medium.com',
    'dev.to',
    'wikipedia.org'
];

// Generate test data
function generateTestData() {
    const resources = [];
    
    testDomains.forEach((domain, index) => {
        resources.push({
            title: `Related: ${domain} Resource`,
            description: `This is a test resource from ${domain}`,
            url: `https://${domain}/resource${index}`
        });
        
        resources.push({
            title: `Source from ${domain}`,
            description: `This is a test source from ${domain}`,
            url: `https://${domain}/source${index}`
        });
    });
    
    return {
        resources: resources
    };
}

// Function to create a test panel
function createTestPanel() {
    // Create container
    const container = document.createElement('div');
    container.id = 'tiptop-test-container';
    container.style.position = 'fixed';
    container.style.top = '20px';
    container.style.right = '20px';
    container.style.width = '350px';
    container.style.maxHeight = '80vh';
    container.style.overflowY = 'auto';
    container.style.backgroundColor = '#fff';
    container.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';
    container.style.borderRadius = '8px';
    container.style.padding = '20px';
    container.style.zIndex = '9999';
    container.style.fontFamily = 'Arial, sans-serif';
    
    // Add header
    const header = document.createElement('h2');
    header.textContent = 'TipTop Icon Test';
    header.style.margin = '0 0 15px 0';
    header.style.color = '#3498db';
    container.appendChild(header);
    
    // Add close button
    const closeButton = document.createElement('button');
    closeButton.textContent = 'Close';
    closeButton.style.position = 'absolute';
    closeButton.style.top = '15px';
    closeButton.style.right = '15px';
    closeButton.style.backgroundColor = '#e74c3c';
    closeButton.style.color = 'white';
    closeButton.style.border = 'none';
    closeButton.style.borderRadius = '4px';
    closeButton.style.padding = '5px 10px';
    closeButton.style.cursor = 'pointer';
    closeButton.onclick = () => container.remove();
    container.appendChild(closeButton);
    
    // Add resources section
    const resourcesSection = document.createElement('div');
    resourcesSection.id = 'tiptop-resources-tips-section';
    
    const resourcesHeader = document.createElement('h3');
    resourcesHeader.textContent = 'Tips & Links';
    resourcesHeader.style.margin = '15px 0 10px 0';
    resourcesHeader.style.color = '#2c3e50';
    resourcesSection.appendChild(resourcesHeader);
    
    const resourcesList = document.createElement('ul');
    resourcesList.id = 'tiptop-resources-tips';
    resourcesList.style.listStyleType = 'none';
    resourcesList.style.padding = '0';
    resourcesList.style.margin = '0';
    resourcesSection.appendChild(resourcesList);
    
    container.appendChild(resourcesSection);
    
    document.body.appendChild(container);
    
    return container;
}

// Function to decode HTML entities
function decodeHtml(html) {
    if (!html) {
        return '';
    }
    const txt = document.createElement('textarea');
    txt.innerHTML = html;
    return txt.value;
}

// Function to display test resources
function displayTestResources() {
    const testPanel = createTestPanel();
    const data = generateTestData();
    
    // Display resources
    if (data.resources && data.resources.length > 0) {
        const resourcesTipsElement = document.getElementById('tiptop-resources-tips');
        if (resourcesTipsElement) {
            let resourcesHtml = '';
            data.resources.forEach(resource => {
                // Check if it's a tip or a link based on the title format
                const isRelatedLink = resource.title.startsWith('Related:');
                
                // Create a card-like item similar to the Related Resources section
                resourcesHtml += `
                <li style="margin-bottom: 12px; background-color: #f8f9fa; border-radius: 8px; border: 1px solid #e0e6ed; overflow: hidden; transition: all 0.2s ease;">
                    <div style="padding: 12px;">
                        <div style="font-weight: 600; margin-bottom: 4px; color: #2980b9;">${decodeHtml(resource.title)}</div>
                        <div style="font-size: 13px; color: #546e7a; margin-bottom: 8px;">${decodeHtml(resource.description)}</div>`;
                
                if (resource.url) {
                    // Get domain from URL
                    let domain = '';
                    let urlObj;
                    try {
                        urlObj = new URL(resource.url);
                        domain = urlObj.hostname;
                    } catch (e) {
                        console.error('Failed to parse URL:', e);
                    }
                    
                    // Generate unique IDs for SVG elements to prevent conflicts
                    const uniqueId = Math.random().toString(36).substring(2, 10);
                    
                    // Better fallback SVG icons with gradients, colors and backgrounds
                    const fallbackIconSvg = isRelatedLink ?
                        // Resource icon - book with gradient and background
                        `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                            <defs>
                                <linearGradient id="resourceGradient-${uniqueId}" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stop-color="#4facfe"/>
                                    <stop offset="100%" stop-color="#00f2fe"/>
                                </linearGradient>
                                <linearGradient id="resourceBgGradient-${uniqueId}" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stop-color="#4facfe" stop-opacity="0.2"/>
                                    <stop offset="100%" stop-color="#00f2fe" stop-opacity="0.1"/>
                                </linearGradient>
                            </defs>
                            <rect width="20" height="20" x="2" y="2" rx="4" fill="url(#resourceBgGradient-${uniqueId})"/>
                            <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="url(#resourceGradient-${uniqueId})"/>
                            <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" stroke="url(#resourceGradient-${uniqueId})"/>
                            <line x1="8" y1="7" x2="16" y2="7" stroke="url(#resourceGradient-${uniqueId})"/>
                            <line x1="8" y1="11" x2="16" y2="11" stroke="url(#resourceGradient-${uniqueId})"/>
                            <line x1="8" y1="15" x2="12" y2="15" stroke="url(#resourceGradient-${uniqueId})"/>
                        </svg>` :
                        // Source icon - link with gradient and background
                        `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                            <defs>
                                <linearGradient id="linkGradient-${uniqueId}" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stop-color="#6a11cb"/>
                                    <stop offset="100%" stop-color="#2575fc"/>
                                </linearGradient>
                                <linearGradient id="linkBgGradient-${uniqueId}" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stop-color="#6a11cb" stop-opacity="0.2"/>
                                    <stop offset="100%" stop-color="#2575fc" stop-opacity="0.1"/>
                                </linearGradient>
                            </defs>
                            <rect width="20" height="20" x="2" y="2" rx="4" fill="url(#linkBgGradient-${uniqueId})"/>
                            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" stroke="url(#linkGradient-${uniqueId})"/>
                            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" stroke="url(#linkGradient-${uniqueId})"/>
                        </svg>`;
                    
                    // Simplified approach with direct SVG icons
                    let iconHtml;
                    
                    if (domain) {
                        // Generate unique IDs for domain-based SVGs
                        const domainUniqueId = Math.random().toString(36).substring(2, 10);
                        
                        // Use a colored SVG with the site's initial letter
                        const color1 = getRandomColor(domain);
                        const color2 = getRandomColor(domain, 40);
                        
                        // Debug log to confirm code execution
                        console.log('TipTop Test: Creating icon for domain', domain, 'with colors', color1, color2);
                        
                        iconHtml = isRelatedLink ?
                            // Resource icon (book) with domain-based color
                            `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                                <defs>
                                    <linearGradient id="grad-${domainUniqueId}" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" stop-color="${color1}"/>
                                        <stop offset="100%" stop-color="${color2}"/>
                                    </linearGradient>
                                </defs>
                                <rect width="20" height="20" x="2" y="2" rx="4" fill="url(#grad-${domainUniqueId})" opacity="0.2"/>
                                <text x="12" y="16" font-family="Arial" font-size="10" font-weight="bold" fill="${color1}" text-anchor="middle">${domain.charAt(0).toUpperCase()}</text>
                                <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="${color1}"/>
                                <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" stroke="${color1}"/>
                            </svg>` :
                            // Source icon (link) with domain-based color
                            `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                                <defs>
                                    <linearGradient id="grad-${domainUniqueId}" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" stop-color="${color1}"/>
                                        <stop offset="100%" stop-color="${color2}"/>
                                    </linearGradient>
                                </defs>
                                <rect width="20" height="20" x="2" y="2" rx="4" fill="url(#grad-${domainUniqueId})" opacity="0.2"/>
                                <text x="12" y="16" font-family="Arial" font-size="10" font-weight="bold" fill="${color1}" text-anchor="middle">${domain.charAt(0).toUpperCase()}</text>
                                <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" stroke="${color1}"/>
                                <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" stroke="${color1}"/>
                            </svg>`;
                    } else {
                        // No domain, use default SVG fallback
                        iconHtml = fallbackIconSvg;
                    }
                    
                    resourcesHtml += `
                        <div style="margin-top: 8px;">
                            <a href="${decodeHtml(resource.url)}" target="_blank" rel="noopener noreferrer" style="display: inline-block; padding: 5px 10px; background-color: #e8f4fd; border-radius: 4px; font-size: 13px; border: 1px solid #c1d9f0; transition: all 0.2s ease; color: #2980b9; text-decoration: none;">
                                ${iconHtml}
                                ${isRelatedLink ? 'Visit Resource' : 'View Source'}
                            </a>
                        </div>`;
                }
                
                resourcesHtml += `
                    </div>
                </li>`;
            });
            resourcesTipsElement.innerHTML = resourcesHtml;
        }
    }
}

// Run the test
console.log('TipTop Icon Test: Starting test...');
displayTestResources();
console.log('TipTop Icon Test: Test panel created. Check the top-right corner of the page.');
