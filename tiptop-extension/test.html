<!DOCTYPE html>
<html>
<head>
    <title>TipTop Test</title>
    <style>
        /* Social Features Styling */
        .tiptop-collaboration-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .tiptop-collaboration-title {
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }

        .tiptop-collaboration-title svg {
            margin-right: 6px;
            color: #3498db;
        }

        .tiptop-collaboration-toggle {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .tiptop-collaboration-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .tiptop-toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .tiptop-toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .tiptop-toggle-slider {
            background-color: #3498db;
        }

        input:checked + .tiptop-toggle-slider:before {
            transform: translateX(20px);
        }
    </style>
</head>
<body>
    <h1>TipTop Test</h1>
    
    <div id="social-section">
        <div class="tiptop-collaboration-header">
            <div class="tiptop-collaboration-title">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
                Who's on the same page?
            </div>
            <label class="tiptop-collaboration-toggle">
                <input type="checkbox" id="social-toggle">
                <span class="tiptop-toggle-slider"></span>
            </label>
        </div>
        <div id="social-content" style="display: none;">
            Social content goes here
        </div>
    </div>
    
    <div id="ai-section">
        <div class="tiptop-collaboration-header">
            <div class="tiptop-collaboration-title">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                </svg>
                AI-Powered Insights
            </div>
            <label class="tiptop-collaboration-toggle">
                <input type="checkbox" id="ai-toggle" checked>
                <span class="tiptop-toggle-slider"></span>
            </label>
        </div>
        <div id="ai-content">
            AI content goes here
        </div>
    </div>
    
    <script>
        document.getElementById('social-toggle').addEventListener('change', function() {
            const socialContent = document.getElementById('social-content');
            const aiToggle = document.getElementById('ai-toggle');
            const aiContent = document.getElementById('ai-content');
            
            socialContent.style.display = this.checked ? 'block' : 'none';
            
            if (this.checked && aiToggle.checked) {
                aiToggle.checked = false;
                aiContent.style.display = 'none';
            }
        });
        
        document.getElementById('ai-toggle').addEventListener('change', function() {
            const aiContent = document.getElementById('ai-content');
            const socialToggle = document.getElementById('social-toggle');
            const socialContent = document.getElementById('social-content');
            
            aiContent.style.display = this.checked ? 'block' : 'none';
            
            if (this.checked && socialToggle.checked) {
                socialToggle.checked = false;
                socialContent.style.display = 'none';
            }
        });
    </script>
</body>
</html>
