<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TipTop Icon Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ddd;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        .icon-container {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .icon-test {
            border: 1px solid #eee;
            padding: 10px;
            border-radius: 4px;
            width: 200px;
        }
        h1, h2 {
            color: #3498db;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>TipTop Icon Test Page</h1>
    <p>This page tests the icon generation for the TipTop extension.</p>
    
    <div class="test-container">
        <h2>Test Domain-Based Icons</h2>
        <button id="generate-domain-icons">Generate Domain Icons</button>
        <div id="domain-icons" class="icon-container"></div>
    </div>

    <div class="test-container">
        <h2>Test Fallback Icons</h2>
        <button id="generate-fallback-icons">Generate Fallback Icons</button>
        <div id="fallback-icons" class="icon-container"></div>
    </div>

    <script>
        // Function to generate a consistent color based on domain name
        function getRandomColor(domain, offset = 0) {
            if (!domain) return '#3498db';
            
            // Create a hash from the domain string
            let hash = 0;
            for (let i = 0; i < domain.length; i++) {
                hash = domain.charCodeAt(i) + ((hash << 5) - hash);
            }
            
            // Add the offset to create a related but different color
            hash += offset;
            
            // Convert to a good-looking hex color
            const hue = ((hash % 360) + 360) % 360; // 0-359 degrees
            const saturation = 70 + (hash % 20); // 70-89%
            const lightness = 45 + (hash % 15); // 45-59%
            
            return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
        }

        // Test domains
        const testDomains = [
            'google.com',
            'github.com',
            'stackoverflow.com',
            'medium.com',
            'dev.to',
            'wikipedia.org'
        ];

        // Generate domain-based icons
        document.getElementById('generate-domain-icons').addEventListener('click', function() {
            const container = document.getElementById('domain-icons');
            container.innerHTML = '';
            
            testDomains.forEach(domain => {
                const color1 = getRandomColor(domain);
                const color2 = getRandomColor(domain, 40);
                const domainUniqueId = Math.random().toString(36).substring(2, 10);
                
                // Create resource icon
                const resourceIcon = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                    <defs>
                        <linearGradient id="grad-${domainUniqueId}-1" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="${color1}"/>
                            <stop offset="100%" stop-color="${color2}"/>
                        </linearGradient>
                    </defs>
                    <rect width="20" height="20" x="2" y="2" rx="4" fill="url(#grad-${domainUniqueId}-1)" opacity="0.2"/>
                    <text x="12" y="16" font-family="Arial" font-size="10" font-weight="bold" fill="${color1}" text-anchor="middle">${domain.charAt(0).toUpperCase()}</text>
                    <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="${color1}"/>
                    <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" stroke="${color1}"/>
                </svg>`;
                
                // Create source icon
                const sourceIcon = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                    <defs>
                        <linearGradient id="grad-${domainUniqueId}-2" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="${color1}"/>
                            <stop offset="100%" stop-color="${color2}"/>
                        </linearGradient>
                    </defs>
                    <rect width="20" height="20" x="2" y="2" rx="4" fill="url(#grad-${domainUniqueId}-2)" opacity="0.2"/>
                    <text x="12" y="16" font-family="Arial" font-size="10" font-weight="bold" fill="${color1}" text-anchor="middle">${domain.charAt(0).toUpperCase()}</text>
                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" stroke="${color1}"/>
                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" stroke="${color1}"/>
                </svg>`;
                
                // Create test element
                const testElement = document.createElement('div');
                testElement.className = 'icon-test';
                testElement.innerHTML = `
                    <strong>${domain}</strong><br>
                    Resource: ${resourceIcon}<br>
                    Source: ${sourceIcon}
                `;
                
                container.appendChild(testElement);
            });
        });

        // Generate fallback icons
        document.getElementById('generate-fallback-icons').addEventListener('click', function() {
            const container = document.getElementById('fallback-icons');
            container.innerHTML = '';
            
            // Generate unique IDs
            const uniqueId1 = Math.random().toString(36).substring(2, 10);
            const uniqueId2 = Math.random().toString(36).substring(2, 10);
            
            // Resource icon
            const resourceIcon = `
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                <defs>
                    <linearGradient id="resourceGradient-${uniqueId1}" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stop-color="#4facfe"/>
                        <stop offset="100%" stop-color="#00f2fe"/>
                    </linearGradient>
                    <linearGradient id="resourceBgGradient-${uniqueId1}" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stop-color="#4facfe" stop-opacity="0.2"/>
                        <stop offset="100%" stop-color="#00f2fe" stop-opacity="0.1"/>
                    </linearGradient>
                </defs>
                <rect width="20" height="20" x="2" y="2" rx="4" fill="url(#resourceBgGradient-${uniqueId1})"/>
                <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="url(#resourceGradient-${uniqueId1})"/>
                <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" stroke="url(#resourceGradient-${uniqueId1})"/>
                <line x1="8" y1="7" x2="16" y2="7" stroke="url(#resourceGradient-${uniqueId1})"/>
                <line x1="8" y1="11" x2="16" y2="11" stroke="url(#resourceGradient-${uniqueId1})"/>
                <line x1="8" y1="15" x2="12" y2="15" stroke="url(#resourceGradient-${uniqueId1})"/>
            </svg>`;
            
            // Source icon
            const sourceIcon = `
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: middle; margin-right: 5px;">
                <defs>
                    <linearGradient id="linkGradient-${uniqueId2}" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stop-color="#6a11cb"/>
                        <stop offset="100%" stop-color="#2575fc"/>
                    </linearGradient>
                    <linearGradient id="linkBgGradient-${uniqueId2}" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stop-color="#6a11cb" stop-opacity="0.2"/>
                        <stop offset="100%" stop-color="#2575fc" stop-opacity="0.1"/>
                    </linearGradient>
                </defs>
                <rect width="20" height="20" x="2" y="2" rx="4" fill="url(#linkBgGradient-${uniqueId2})"/>
                <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" stroke="url(#linkGradient-${uniqueId2})"/>
                <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" stroke="url(#linkGradient-${uniqueId2})"/>
            </svg>`;
            
            // Create test element
            const testElement = document.createElement('div');
            testElement.className = 'icon-test';
            testElement.innerHTML = `
                <strong>Fallback Icons</strong><br>
                Resource: ${resourceIcon}<br>
                Source: ${sourceIcon}
            `;
            
            container.appendChild(testElement);
        });
    </script>
</body>
</html>
