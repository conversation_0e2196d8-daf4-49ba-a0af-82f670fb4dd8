#!/bin/bash

# <PERSON><PERSON><PERSON> to prepare the TipTop Chrome extension for publishing
# This script removes test files and creates a zip file for the Chrome Web Store

# Set the extension directory
EXTENSION_DIR="extension"
OUTPUT_ZIP="tiptop-extension.zip"

# Check if the extension directory exists
if [ ! -d "$EXTENSION_DIR" ]; then
  echo "Error: Extension directory '$EXTENSION_DIR' not found."
  exit 1
fi

# Create a temporary directory for the cleaned extension
TEMP_DIR=$(mktemp -d)
echo "Created temporary directory: $TEMP_DIR"

# Copy all files to the temporary directory
cp -r "$EXTENSION_DIR"/* "$TEMP_DIR"

# Remove test files
echo "Removing test files..."
rm -f "$TEMP_DIR"/test*.html
rm -f "$TEMP_DIR"/*.map
rm -f "$TEMP_DIR"/.DS_Store
rm -rf "$TEMP_DIR"/.git
rm -f "$TEMP_DIR"/.gitignore
rm -f "$TEMP_DIR"/README.md
rm -rf "$TEMP_DIR"/node_modules
rm -f "$TEMP_DIR"/package*.json

# Create zip file
echo "Creating zip file..."
cd "$TEMP_DIR"
zip -r "../../$OUTPUT_ZIP" ./* -x "*.DS_Store" "*.git*" "*.map" "test*" "*.zip"
cd - > /dev/null

# Clean up
echo "Cleaning up..."
rm -rf "$TEMP_DIR"

echo "Done! Extension package created: $OUTPUT_ZIP"
echo "You can now upload this file to the Chrome Web Store."
