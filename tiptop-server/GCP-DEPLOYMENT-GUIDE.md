# TipTop GCP Deployment Guide

This guide explains how to deploy TipTop to Google Cloud Platform (GCP) with the complete infrastructure including WebSocket server.

## 🎯 Deployment Options

### Option 1: Fresh Deployment
Use this for new deployments or when you want to start clean.

### Option 2: Zero-Downtime Rolling Update (Recommended for Production)
Use this when you have an existing deployment and want to update without downtime.

### Option 3: Quick Update
Use this for fast code updates with minimal commands.

## 📋 Prerequisites

1. **GCP Project** with billing enabled
2. **GKE Cluster** named `tiptop-cluster` in `us-central1`
3. **Local Tools Installed:**
   - `gcloud` CLI
   - `kubectl`
   - `docker`

## 🚀 Deployment Steps

### Step 1: Prepare Local Environment

```bash
# Copy the tiptop-server folder to your GCP Cloud Shell or local machine
# Make sure you have the latest version with WebSocket server

# Navigate to the server directory
cd tiptop-server
```

### Step 2: Handle Existing TipTop Deployment

#### If TipTop is in SmartParent Namespace (Common Issue):

```bash
# Check for conflicts first
./check-conflicts.sh

# If old TipTop found in smartparent namespace, choose migration strategy:

# Option A: Migrate to separate namespace (RECOMMENDED)
./migrate-from-smartparent.sh YOUR_PROJECT_ID --migrate-only

# Option B: Update in-place (keeps mixed namespace)
./migrate-from-smartparent.sh YOUR_PROJECT_ID --replace-in-place
```

#### If TipTop is in Separate Namespace:

```bash
# Check what's currently deployed
./cleanup-old-deployment.sh

# Follow the prompts to clean up old resources
```

### Step 3: Deploy to GCP

#### For Fresh Deployment:
```bash
# Replace YOUR_PROJECT_ID with your actual GCP project ID
./deploy-to-gcp.sh YOUR_PROJECT_ID
```

#### For Zero-Downtime Rolling Update (Recommended):
```bash
# This performs a rolling update with zero downtime
./deploy-to-gcp.sh YOUR_PROJECT_ID --rolling-update

# OR use the quick update script
./quick-update.sh YOUR_PROJECT_ID
```

#### For Standard Update:
```bash
# This skips initial setup and only updates the code
./deploy-to-gcp.sh YOUR_PROJECT_ID --update-only
```

### Step 4: Configure Production Mode

After deployment, configure the system for production:

```bash
# Switch to production mode (this updates secrets and restarts services)
./tiptop-toggle-mode.sh production
```

## 🔍 What Each Script Does

### `deploy-to-gcp.sh`
- ✅ Builds and pushes Docker images to Google Container Registry
- ✅ Deploys Cloud Function to Kubernetes
- ✅ Deploys WebSocket server to Kubernetes
- ✅ Sets up services and ingress
- ✅ Configures SSL certificates

### `tiptop-toggle-mode.sh production`
- ✅ Updates Kubernetes secrets to production values
- ✅ Restarts deployments to pick up new configuration
- ✅ Configures extension to use production URLs

### `cleanup-old-deployment.sh`
- ✅ Shows current deployments
- ✅ Safely removes old resources
- ✅ Preserves data if needed

## 🌐 Expected Results

After successful deployment, you should have:

### Services Running:
- **Cloud Function API**: `https://tiptop.qubitrhythm.com`
- **WebSocket Server**: `wss://ws.tiptop.qubitrhythm.com`
- **PostgreSQL Database**: Internal to cluster

### Kubernetes Resources:
```bash
# Check deployment status
kubectl get pods -n tiptop
kubectl get services -n tiptop
kubectl get ingress -n tiptop
```

## 🔧 Troubleshooting

### Common Issues:

1. **SSL Certificate Not Ready**
   ```bash
   # Check certificate status
   kubectl describe certificate -n tiptop
   
   # Wait for certificates to be issued (can take 5-10 minutes)
   ```

2. **WebSocket Connection Issues**
   ```bash
   # Check WebSocket logs
   kubectl logs deployment/tiptop-websocket -n tiptop -f
   ```

3. **API Not Responding**
   ```bash
   # Check API logs
   kubectl logs deployment/cloud-function-tiptop-deployment -n tiptop -f
   ```

### Monitoring Commands:
```bash
# Watch all pods
kubectl get pods -n tiptop -w

# Check ingress IP addresses
kubectl get ingress -n tiptop

# View logs
kubectl logs deployment/cloud-function-tiptop-deployment -n tiptop -f
kubectl logs deployment/tiptop-websocket -n tiptop -f
```

## 🎯 Post-Deployment Steps

1. **Update DNS Records** (if needed)
   - Point `tiptop.qubitrhythm.com` to the ingress IP
   - Point `ws.tiptop.qubitrhythm.com` to the WebSocket ingress IP

2. **Test the Endpoints**
   ```bash
   # Test API
   curl -X POST https://tiptop.qubitrhythm.com/tiptop \
     -H "Content-Type: application/json" \
     -d '{"url":"https://example.com"}'
   
   # Test WebSocket (use a WebSocket client)
   # Connect to: wss://ws.tiptop.qubitrhythm.com
   ```

3. **Update Extension** (for users)
   - Extension will automatically use production URLs
   - Users should reload the extension after deployment

## 📊 Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Extension     │────│   Load Balancer  │────│  Cloud Function │
│   (Browser)     │    │   (Ingress)      │    │   (API Server)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                │                        │
                       ┌──────────────────┐    ┌─────────────────┐
                       │  WebSocket LB    │────│ WebSocket Server│
                       │   (Ingress)      │    │  (Social Features)│
                       └──────────────────┘    └─────────────────┘
                                                        │
                                                        │
                                               ┌─────────────────┐
                                               │   PostgreSQL    │
                                               │   (Database)    │
                                               └─────────────────┘
```

## 🔄 Development Workflow

1. **Local Development**: Use `./tiptop-toggle-mode.sh localtest --real-api`
2. **Testing Against Production**: Use `./tiptop-toggle-mode.sh production`
3. **Deploy Updates**: Use `./deploy-to-gcp.sh YOUR_PROJECT_ID --update-only`

This ensures you can develop locally while testing against production infrastructure when needed!
