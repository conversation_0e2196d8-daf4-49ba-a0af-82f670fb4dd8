#!/bin/bash

# TipTop Quick Update Script
# This script performs a zero-downtime rolling update of the TipTop services

# Function to handle errors
handle_error() {
    echo "❌ Error occurred in quick update script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# Function to show usage
show_usage() {
    echo "Usage: $0 <GCP_PROJECT_ID>"
    echo ""
    echo "This script performs a zero-downtime rolling update of TipTop services."
    echo "It builds new images and updates the running deployments without downtime."
    echo ""
    echo "Arguments:"
    echo "  GCP_PROJECT_ID  - Your GCP project ID"
    echo ""
    echo "Example:"
    echo "  $0 my-gcp-project-123"
    exit 1
}

# Check arguments
if [ -z "$1" ]; then
    echo "❌ Error: GCP Project ID is required"
    show_usage
fi

GCP_PROJECT_ID=$1

echo "🚀 TipTop Quick Rolling Update"
echo "📋 Project ID: $GCP_PROJECT_ID"
echo "🔄 This will perform a zero-downtime update"
echo ""

# Check if required tools are installed
echo "🔍 Checking required tools..."
command -v gcloud >/dev/null 2>&1 || { echo "❌ gcloud CLI is required but not installed."; exit 1; }
command -v kubectl >/dev/null 2>&1 || { echo "❌ kubectl is required but not installed."; exit 1; }
command -v docker >/dev/null 2>&1 || { echo "❌ docker is required but not installed."; exit 1; }

# Set GCP project
echo "🔧 Setting GCP project..."
gcloud config set project $GCP_PROJECT_ID

# Configure Docker for GCR
echo "🐳 Configuring Docker for Google Container Registry..."
gcloud auth configure-docker

# Check current deployment status
echo "📊 Checking current deployment status..."
if ! kubectl get deployment cloud-function-tiptop-deployment -n tiptop >/dev/null 2>&1; then
    echo "❌ Cloud Function deployment not found!"
    echo "💡 You may need to run a full deployment first:"
    echo "   ./deploy-to-gcp.sh $GCP_PROJECT_ID"
    exit 1
fi

echo "✅ Found existing deployments"
echo ""

# Build and push Cloud Function image with timestamp tag
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
echo "🏗️  Building Cloud Function image with tag: $TIMESTAMP..."
cd cloud-function-tiptop
docker build -t gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:$TIMESTAMP .
docker tag gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:$TIMESTAMP gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:latest

echo "📤 Pushing Cloud Function image..."
docker push gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:$TIMESTAMP
docker push gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:latest
cd ..

# Build and push WebSocket server image with timestamp tag
echo "🏗️  Building WebSocket server image with tag: $TIMESTAMP..."
cd websocket-server
docker build -t gcr.io/$GCP_PROJECT_ID/tiptop-websocket:$TIMESTAMP .
docker tag gcr.io/$GCP_PROJECT_ID/tiptop-websocket:$TIMESTAMP gcr.io/$GCP_PROJECT_ID/tiptop-websocket:latest

echo "📤 Pushing WebSocket server image..."
docker push gcr.io/$GCP_PROJECT_ID/tiptop-websocket:$TIMESTAMP
docker push gcr.io/$GCP_PROJECT_ID/tiptop-websocket:latest
cd ..

# Perform rolling update for Cloud Function
echo "🔄 Performing rolling update for Cloud Function..."
kubectl set image deployment/cloud-function-tiptop-deployment \
    cloud-function-tiptop=gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:$TIMESTAMP -n tiptop

echo "📊 Monitoring Cloud Function rollout..."
kubectl rollout status deployment/cloud-function-tiptop-deployment -n tiptop --timeout=300s

# Perform rolling update for WebSocket server (if it exists)
if kubectl get deployment tiptop-websocket -n tiptop >/dev/null 2>&1; then
    echo "🔄 Performing rolling update for WebSocket server..."
    kubectl set image deployment/tiptop-websocket \
        tiptop-websocket=gcr.io/$GCP_PROJECT_ID/tiptop-websocket:$TIMESTAMP -n tiptop
    
    echo "📊 Monitoring WebSocket server rollout..."
    kubectl rollout status deployment/tiptop-websocket -n tiptop --timeout=300s
else
    echo "⚠️  WebSocket deployment not found, skipping WebSocket update"
fi

echo ""
echo "✅ Rolling update completed successfully!"
echo ""
echo "📊 Current Status:"
kubectl get pods -n tiptop
echo ""
echo "🔍 Verify the update:"
echo "  - API: curl https://tiptop.qubitrhythm.com/"
echo "  - Check logs: kubectl logs deployment/cloud-function-tiptop-deployment -n tiptop"
echo "  - WebSocket logs: kubectl logs deployment/tiptop-websocket -n tiptop"
echo ""
echo "🎯 The update was performed with zero downtime!"
echo "Users should not have experienced any service interruption."
