# TipTop Server Deployment

This directory contains the TipTop server implementation with Kubernetes deployment configurations.

## 🚀 Quick Start Guide

### Step 1: Check for Conflicts
```bash
./check-conflicts.sh
```

### Step 2: Choose Deployment Strategy

#### If you have OLD TipTop in smartparent namespace (RECOMMENDED):
```bash
# Stop old TipTop and deploy new one (clean approach)
./stop-old-deploy-new.sh YOUR_PROJECT_ID
```

#### If you want to migrate without downtime:
```bash
# Migrate to separate namespace (complex)
./migrate-from-smartparent.sh YOUR_PROJECT_ID --migrate-only
```

#### If you have NO existing TipTop:
```bash
# Fresh deployment
./deploy-to-gcp.sh YOUR_PROJECT_ID
```

#### For quick code updates:
```bash
./quick-update.sh YOUR_PROJECT_ID
```

### Step 3: Configure Mode
```bash
# Switch to production mode
../tiptop-toggle-mode.sh production

# OR local test mode
../tiptop-toggle-mode.sh localtest --real-api
```

## 📋 Script Reference

| Script | Purpose | When to Use |
|--------|---------|-------------|
| `check-conflicts.sh` | Detect deployment conflicts | **Always run first** |
| `stop-old-deploy-new.sh` | Stop old, deploy new | **Old TipTop in smartparent ns** |
| `migrate-from-smartparent.sh` | Zero-downtime migration | Complex migration scenarios |
| `deploy-to-gcp.sh` | Fresh/update deployment | New deployment or tiptop ns |
| `quick-update.sh` | Fast code updates | Quick updates only |

## 📚 Documentation

- `GCP-DEPLOYMENT-GUIDE.md` - Complete deployment guide
- `CONFLICT-ANALYSIS.md` - Conflict analysis details
   - Deploy PostgreSQL and the cloud function
4. Wait for all services to be up and running
5. Load the extension in Chrome:
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in the top-right corner)
   - Click "Load unpacked" and select the `tiptop-extension` directory
   - The extension should now be loaded with the name "TipTop Dev"

## Testing the Extension

1. Navigate to any webpage
2. Click on the TipTop Dev extension icon in the Chrome toolbar
3. The extension should connect to your local development environment at http://localhost:30080 and display the TipTop panel

The service is configured to use NodePort, which means:
- It's directly accessible at http://localhost:30080 (HTTP) and https://localhost:30443 (HTTPS)
- No port forwarding is needed
- The service remains accessible as long as the Kubernetes cluster is running

## Updating the Cloud Function

If you make changes to the cloud function code, you can update the deployment without redeploying everything:

1. Make your changes to the code in the `cloud-function-tiptop` directory
2. Run the update script:
   ```
   ./tiptop-update.sh
   ```

## Troubleshooting

- If the extension can't connect to the local server, check that the service is running and the NodePort is accessible:
  ```
  kubectl get service cloud-function-tiptop-service -n tiptop
  ```

- If you need to update the service configuration (e.g., change the NodePort):
  ```
  ./update-service.sh
  ```
- To view logs from the cloud function:
  ```
  kubectl logs -f deployment/cloud-function-tiptop-deployment -n tiptop
  ```
- To restart the cloud function:
  ```
  kubectl rollout restart deployment/cloud-function-tiptop-deployment -n tiptop
  ```

## Cleaning Up

To remove all resources created by the deployment script:

```
kubectl delete namespace tiptop
kubectl delete pv tiptop-postgres-pv
```

If you want to completely clean up, you can also remove the data directory:

```
rm -rf /Users/<USER>/AI/tiptop/tiptop-data
```
