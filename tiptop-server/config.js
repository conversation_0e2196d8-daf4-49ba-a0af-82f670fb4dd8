/**
 * Global Configuration Module for TipTop
 * 
 * This module provides a centralized configuration system that supports
 * switching between test and production environments.
 */

// Load environment variables
require('dotenv').config();

// Determine if we're in test mode
const isTestMode = process.env.TIPTOP_TEST_MODE === 'true';

// Configuration object
const config = {
  // Environment
  isTestMode: isTestMode,
  environment: isTestMode ? 'test' : 'production',
  
  // Kubernetes namespace
  namespace: 'tiptop',
  
  // Secret file names
  secretFiles: {
    postgres: isTestMode ? 'tiptop-postgres-secrets-test.yaml' : 'tiptop-postgres-secrets.yaml',
    cloudFunction: isTestMode ? 'tiptop-cloud-function-secrets-test.yaml' : 'tiptop-cloud-function-secrets.yaml',
    websocket: isTestMode ? 'tiptop-websocket-secrets-test.yaml' : 'tiptop-websocket-secrets.yaml',
  },
  
  // Server URLs
  serverUrls: {
    cloudFunction: isTestMode 
      ? 'http://localhost:30080' 
      : 'https://tiptop.qubitrhythm.com',
    websocket: isTestMode 
      ? 'ws://localhost:8080' 
      : 'wss://ws.tiptop.qubitrhythm.com',
  },
  
  // Database configuration
  database: {
    host: isTestMode ? 'tiptop-postgres.tiptop.svc.cluster.local' : 'tiptop-postgres.tiptop.svc.cluster.local',
    port: 5432,
    name: 'tiptop',
    user: 'postgres',
  },
};

// Export the configuration
module.exports = config;
