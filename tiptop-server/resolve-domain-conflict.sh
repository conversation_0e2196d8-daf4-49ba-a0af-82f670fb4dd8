#!/bin/bash

# TipTop Domain Conflict Resolution Script
# This script helps resolve the domain conflict where SmartParent is using tiptop.qubitrhythm.com

# Function to handle errors
handle_error() {
    echo "❌ Error occurred in domain resolution script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

echo "🌐 TipTop Domain Conflict Resolution"
echo "===================================="
echo ""

# Check current domain usage
echo "🔍 Analyzing current domain usage..."
echo ""

echo "📋 SmartParent ingress configuration:"
if kube<PERSON>l get ingress -n smartparent >/dev/null 2>&1; then
    kubectl get ingress -n smartparent -o custom-columns="NAME:.metadata.name,HOSTS:.spec.rules[*].host,ADDRESS:.status.loadBalancer.ingress[*].ip"
else
    echo "   No ingress found in smartparent namespace"
fi
echo ""

echo "📋 TipTop ingress configuration:"
if kube<PERSON>l get ingress -n tiptop >/dev/null 2>&1; then
    kube<PERSON>l get ingress -n tiptop -o custom-columns="NAME:.metadata.name,HOSTS:.spec.rules[*].host,ADDRESS:.status.loadBalancer.ingress[*].ip"
else
    echo "   No ingress found in tiptop namespace"
fi
echo ""

# Check which service is actually serving tiptop.qubitrhythm.com
echo "🔍 Testing which service is currently serving tiptop.qubitrhythm.com..."
if curl -s -I https://tiptop.qubitrhythm.com >/dev/null 2>&1; then
    echo "✅ tiptop.qubitrhythm.com is currently accessible"
    
    # Try to determine which service is responding
    response=$(curl -s https://tiptop.qubitrhythm.com/ 2>/dev/null || echo "")
    if echo "$response" | grep -qi "smartparent"; then
        echo "🔍 Appears to be serving SmartParent content"
    elif echo "$response" | grep -qi "tiptop"; then
        echo "🔍 Appears to be serving TipTop content"
    else
        echo "🔍 Unable to determine which service is responding"
    fi
else
    echo "⚠️  tiptop.qubitrhythm.com is not currently accessible"
fi
echo ""

echo "🎯 Resolution Options"
echo "===================="
echo ""

echo "📋 Option 1: Migrate TipTop to Separate Namespace (RECOMMENDED)"
echo "----------------------------------------------------------------"
echo "✅ Pros:"
echo "   - Clean separation of services"
echo "   - SmartParent keeps current domain setup"
echo "   - TipTop gets fresh ingress configuration"
echo "   - Easy to manage long-term"
echo ""
echo "⚠️  Considerations:"
echo "   - TipTop will initially get a new load balancer IP"
echo "   - You'll need to update DNS to point to new TipTop"
echo "   - Brief DNS propagation time"
echo ""
echo "🚀 Command:"
echo "   ./migrate-from-smartparent.sh YOUR_PROJECT_ID --migrate-only"
echo ""

echo "📋 Option 2: Update SmartParent to Use Different Domain"
echo "-------------------------------------------------------"
echo "✅ Pros:"
echo "   - TipTop gets its intended domain immediately"
echo "   - No DNS changes needed for TipTop"
echo ""
echo "⚠️  Considerations:"
echo "   - Requires updating SmartParent configuration"
echo "   - May impact SmartParent users"
echo "   - More complex migration"
echo ""
echo "🚀 Commands:"
echo "   # 1. Update SmartParent ingress to use different domain"
echo "   # 2. Then migrate TipTop normally"
echo ""

echo "📋 Option 3: Coordinate Domain Handover"
echo "---------------------------------------"
echo "✅ Pros:"
echo "   - Seamless domain transition"
echo "   - No service interruption"
echo ""
echo "⚠️  Considerations:"
echo "   - Requires careful coordination"
echo "   - More complex process"
echo ""

echo ""
echo "🎯 Recommended Action Plan"
echo "========================="
echo ""
echo "For your situation, I recommend Option 1:"
echo ""
echo "1️⃣  **Migrate TipTop to separate namespace:**"
echo "   ./migrate-from-smartparent.sh YOUR_PROJECT_ID --migrate-only"
echo ""
echo "2️⃣  **Test new TipTop deployment:**"
echo "   # Get new load balancer IP"
echo "   kubectl get ingress -n tiptop"
echo "   # Test with IP directly or temporary DNS"
echo ""
echo "3️⃣  **Update DNS when ready:**"
echo "   # Point tiptop.qubitrhythm.com to new TipTop load balancer"
echo "   # Point ws.tiptop.qubitrhythm.com to new WebSocket load balancer"
echo ""
echo "4️⃣  **Clean up old TipTop from SmartParent namespace:**"
echo "   kubectl delete deployment cloud-function-tiptop-deployment -n smartparent"
echo "   kubectl delete service cloud-function-tiptop-service -n smartparent"
echo "   kubectl delete ingress <tiptop-ingress-name> -n smartparent"
echo ""

echo "💡 This approach ensures:"
echo "   ✅ Zero downtime for both services"
echo "   ✅ Clean separation"
echo "   ✅ Easy rollback if needed"
echo "   ✅ No impact on SmartParent"
echo ""

echo "🚀 Ready to proceed? Run:"
echo "   ./migrate-from-smartparent.sh YOUR_PROJECT_ID --migrate-only"
