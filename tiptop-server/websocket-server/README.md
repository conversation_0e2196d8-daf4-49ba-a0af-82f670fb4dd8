# TipTop WebSocket Server

This is a WebSocket server for the TipTop Chrome extension, enabling real-time communication between users viewing the same webpage.

## Features

- Real-time messaging between users on the same webpage
- Message persistence in PostgreSQL database
- User presence tracking
- Chat history retrieval
- Works both locally and on GCP Kubernetes

## Prerequisites

- Node.js 14+ and npm
- PostgreSQL database
- Docker (for GCP deployment)
- Google Cloud SDK (for GCP deployment)
- kubectl (for GCP deployment)

## Local Development Setup

1. Clone this repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file with your configuration (see `.env` for an example)
4. Make sure PostgreSQL is running and accessible
5. Run the setup script:
   ```
   ./setup.sh local
   ```
6. Start the development server:
   ```
   npm run dev
   ```
7. The server will be available at http://localhost:8080

## GCP Deployment

1. Make sure you have the Google Cloud SDK installed and configured
2. Make sure you have kubectl configured to access your GKP Kubernetes cluster
3. Run the setup script with your GCP project ID and domain name:
   ```
   ./setup.sh gcp --project-id=YOUR_PROJECT_ID --domain=YOUR_DOMAIN_NAME
   ```
4. The script will:
   - Build and push the Docker image to Google Container Registry
   - Apply the Kubernetes configurations
   - Deploy the WebSocket server to your Kubernetes cluster

## Kubernetes Configuration

The Kubernetes configuration files are in the `k8s` directory:
- `deployment.yaml`: Deployment configuration
- `service.yaml`: Service configuration
- `ingress.yaml`: Ingress configuration

## Environment Variables

- `PORT`: Server port (default: 8080)
- `DB_USER`: PostgreSQL username
- `DB_HOST`: PostgreSQL host
- `DB_NAME`: PostgreSQL database name
- `DB_PASSWORD`: PostgreSQL password
- `DB_PORT`: PostgreSQL port (default: 5432)
- `DEBUG`: Enable detailed logging (default: false)

## API Endpoints

- `/health`: Health check endpoint
- `/status`: Server status endpoint

## WebSocket Protocol

Connect to the WebSocket server with a URL parameter:
```
ws://localhost:8080?url=https://example.com
```

### Message Types

- `chat`: Chat message
- `presence`: User presence update
- `note`: Page note
- `system`: System message
- `history`: Message history
- `users`: Active users list

### Example Messages

Chat message:
```json
{
  "type": "chat",
  "content": "Hello, world!",
  "userId": "user123",
  "userName": "John Doe",
  "url": "https://example.com",
  "timestamp": "2023-05-01T12:00:00.000Z",
  "messageId": "msg_123456789"
}
```

Presence update:
```json
{
  "type": "presence",
  "userId": "user123",
  "userName": "John Doe",
  "url": "https://example.com",
  "timestamp": "2023-05-01T12:00:00.000Z"
}
```

## License

This project is proprietary and confidential.
