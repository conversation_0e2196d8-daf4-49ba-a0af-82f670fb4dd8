{"name": "tiptop-websocket-server", "version": "1.0.0", "description": "WebSocket server for TipTop extension real-time communication", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"dotenv": "^10.0.0", "express": "^4.17.1", "pg": "^8.7.1", "uuid": "^8.3.2", "ws": "^8.2.3"}, "devDependencies": {"nodemon": "^2.0.14"}}