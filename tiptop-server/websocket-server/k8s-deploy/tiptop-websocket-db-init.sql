-- TipTop WebSocket Server Database Initialization

-- Create tiptop_messages table
CREATE TABLE IF NOT EXISTS tiptop_messages (
  id UUID PRIMARY KEY,
  type VARCHAR(50) NOT NULL,
  content TEXT NOT NULL,
  user_id VARCHAR(100) NOT NULL,
  user_name VARCHAR(100) NOT NULL,
  page_url TEXT NOT NULL,
  page_url_hash VARCHAR(100) NOT NULL,
  message_id VARCHAR(100) NOT NULL,
  timestamp TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index on page_url_hash for faster lookups
CREATE INDEX IF NOT EXISTS idx_tiptop_messages_page_url_hash
ON tiptop_messages(page_url_hash);

-- Create index on timestamp for faster sorting
CREATE INDEX IF NOT EXISTS idx_tiptop_messages_timestamp
ON tiptop_messages(timestamp);

-- <PERSON>reate index on message_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_tiptop_messages_message_id
ON tiptop_messages(message_id);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_tiptop_messages_user_id
ON tiptop_messages(user_id);

-- Create index on type for faster filtering
CREATE INDEX IF NOT EXISTS idx_tiptop_messages_type
ON tiptop_messages(type);
