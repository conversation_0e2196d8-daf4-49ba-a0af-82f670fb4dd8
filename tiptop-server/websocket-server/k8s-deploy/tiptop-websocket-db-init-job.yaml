apiVersion: batch/v1
kind: Job
metadata:
  name: tiptop-websocket-db-init-job
  namespace: tiptop
spec:
  template:
    spec:
      containers:
      - name: postgres-client
        image: postgres:14.9
        command:
        - /bin/bash
        - -c
        - |
          echo "Waiting for PostgreSQL to be ready..."
          until pg_isready -h tiptop-postgres.tiptop.svc.cluster.local -p 5432 -U postgres; do
            echo "PostgreSQL is not ready yet. Waiting..."
            sleep 2
          done
          echo "PostgreSQL is ready. Initializing database..."
          PGPASSWORD=$POSTGRES_PASSWORD psql -h tiptop-postgres.tiptop.svc.cluster.local -p 5432 -U postgres -d tiptop -f /init/init.sql
          echo "Database initialization completed."
        env:
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: tiptop-postgres-secrets
              key: POSTGRES_PASSWORD
        volumeMounts:
        - name: init-script
          mountPath: /init
      restartPolicy: OnFailure
      volumes:
      - name: init-script
        configMap:
          name: tiptop-websocket-db-init
