apiVersion: v1
kind: Service
metadata:
  name: tiptop-websocket
  namespace: tiptop
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/websocket-services: "tiptop-websocket"
spec:
  selector:
    app: tiptop-websocket
  ports:
  - port: 80
    targetPort: 8080
    name: http
    protocol: TCP
  - port: 443
    targetPort: 8080
    name: https
    protocol: TCP
  type: ClusterIP
