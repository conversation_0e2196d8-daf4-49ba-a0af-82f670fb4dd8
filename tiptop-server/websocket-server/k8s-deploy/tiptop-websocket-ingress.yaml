apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: tiptop-websocket-ingress
  namespace: tiptop
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod-tiptop
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/acme-challenge-type: http01
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"
    nginx.ingress.kubernetes.io/cors-allow-origin: "chrome-extension://*, https://tiptop.qubitrhythm.com"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/websocket-services: "tiptop-websocket"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - ws.tiptop.qubitrhythm.com
    secretName: tiptop-ws-tls
  rules:
  - host: ws.tiptop.qubitrhythm.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: tiptop-websocket
            port:
              number: 80
