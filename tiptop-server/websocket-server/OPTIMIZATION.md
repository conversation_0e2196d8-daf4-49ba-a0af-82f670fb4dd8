# TipTop WebSocket Server Optimizations

This document outlines the optimizations made to the TipTop WebSocket server to improve reliability and support for multiple concurrent users.

## Optimizations Implemented

### 1. Database Connection Pooling

The PostgreSQL connection pool has been optimized with the following settings:

```javascript
const pool = new Pool({
  // ... existing configuration ...
  max: parseInt(process.env.DB_POOL_SIZE || '20'),     // Maximum number of clients in the pool
  idleTimeoutMillis: 30000,                            // Close idle connections after 30s
  connectionTimeoutMillis: 2000,                       // Connection timeout
  maxUses: 7500                                        // Close connections after 7500 queries
});
```

These settings help prevent connection exhaustion under high load and ensure connections are properly recycled.

### 2. Memory Usage Limits

Memory limits have been implemented to prevent the server from consuming excessive memory:

```javascript
const MEMORY_LIMITS = {
  MAX_MESSAGES_PER_URL: parseInt(process.env.MAX_MESSAGES_PER_URL || '100'),  // Maximum messages stored per URL
  MAX_MESSAGE_SIZE_BYTES: parseInt(process.env.MAX_MESSAGE_SIZE_BYTES || '10000'),  // Maximum size of a single message (10KB)
  MAX_URLS_STORED: parseInt(process.env.MAX_URLS_STORED || '1000'),  // Maximum number of URLs to store messages for
  MESSAGE_EXPIRY_MS: parseInt(process.env.MESSAGE_EXPIRY_MS || '86400000'),  // Messages expire after 24 hours
};
```

The `saveMessage` function has been updated to:
- Truncate messages that exceed the size limit
- Remove oldest messages when the per-URL limit is reached
- Remove oldest URL data when the URL limit is reached
- Automatically expire messages after the configured time period

### 3. Message Batching for Large Rooms

To prevent the event loop from blocking when broadcasting messages to many clients, message sending has been optimized with batching:

```javascript
// Use batching for large rooms to prevent event loop blocking
const BATCH_SIZE = 25; // Process 25 clients at a time
const clients = Array.from(room);
const totalClients = clients.length;

// Function to process a batch of clients
const processBatch = (startIndex) => {
  const endIndex = Math.min(startIndex + BATCH_SIZE, totalClients);
  
  for (let i = startIndex; i < endIndex; i++) {
    // Process client
  }
  
  // If there are more clients to process, schedule the next batch
  if (endIndex < totalClients) {
    setImmediate(() => processBatch(endIndex));
  }
};

// Start processing the first batch
processBatch(0);
```

This approach:
- Processes clients in small batches of 25
- Uses `setImmediate()` to yield to the event loop between batches
- Prevents long-running operations from blocking the server

### 4. Rate Limiting

Rate limiting has been implemented to prevent abuse:

```javascript
const RATE_LIMITS = {
  MESSAGES_PER_MINUTE: parseInt(process.env.RATE_LIMIT_MESSAGES_PER_MINUTE || '30'),  // Maximum messages per minute per client
  PRESENCE_UPDATES_PER_MINUTE: parseInt(process.env.RATE_LIMIT_PRESENCE_PER_MINUTE || '10'),  // Maximum presence updates per minute
  RATE_LIMIT_WINDOW_MS: 60000,  // 1 minute window for rate limiting
};
```

The `isRateLimited` function tracks message timestamps for each user and enforces limits based on the configured thresholds.

### 5. Enhanced Monitoring

The `/status` endpoint has been enhanced to provide detailed information about the server's state:

- Memory usage statistics
- Connection counts by state (open, closing, closed, connecting)
- Room statistics with top 10 most active rooms
- User counts and rate limiting information
- Message counts and storage statistics
- Current configuration settings

## Configuration Options

All optimizations can be configured through environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_POOL_SIZE` | Maximum number of database connections | 20 |
| `MAX_MESSAGES_PER_URL` | Maximum messages stored per URL | 100 |
| `MAX_MESSAGE_SIZE_BYTES` | Maximum size of a single message | 10000 (10KB) |
| `MAX_URLS_STORED` | Maximum number of URLs to store messages for | 1000 |
| `MESSAGE_EXPIRY_MS` | Message expiration time in milliseconds | 86400000 (24 hours) |
| `RATE_LIMIT_MESSAGES_PER_MINUTE` | Maximum messages per minute per client | 30 |
| `RATE_LIMIT_PRESENCE_PER_MINUTE` | Maximum presence updates per minute | 10 |

## Expected Capacity

With these optimizations, the WebSocket server should be able to handle:

- **200-300 concurrent users per room** (same URL)
- **Several thousand total concurrent users** across all rooms

The actual capacity will depend on:
- Server hardware resources (CPU, memory)
- Network bandwidth
- Message frequency and size
- Number of rooms

## Monitoring and Scaling

To monitor the server's performance:

1. Use the enhanced `/status` endpoint to track key metrics
2. Consider integrating with a monitoring system like Prometheus
3. Set up alerts for abnormal conditions (high memory usage, connection counts, etc.)

For scaling:
1. Increase the Kubernetes deployment replicas
2. Adjust resource limits in the deployment configuration
3. Consider using a message broker like Redis for cross-instance communication if needed

## Future Improvements

Additional optimizations that could be implemented:

1. WebSocket compression for large messages
2. Redis-based message queue for reliable delivery
3. Geographic distribution for lower latency
4. Circuit breakers for database operations
5. More sophisticated rate limiting based on user roles or subscription levels
