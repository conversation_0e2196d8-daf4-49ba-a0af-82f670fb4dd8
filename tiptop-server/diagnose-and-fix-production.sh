#!/bin/bash

# TipTop Production Social Features Diagnostic Script
# Run this on GCP Cloud Shell to diagnose social features issues

set -e

echo "🔍 TipTop Production Social Features Diagnostic"
echo "==============================================="
echo ""

# Function to handle errors
handle_error() {
    echo "❌ Error occurred at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# Get current project
PROJECT_ID=$(gcloud config get-value project)
echo "📋 Current GCP Project: $PROJECT_ID"
echo ""

# Connect to the correct cluster
echo "☸️  Connecting to GKE cluster..."
if gcloud container clusters get-credentials smartparent-k8s --zone=us-central1-f 2>/dev/null; then
    echo "✅ Connected to smartparent-k8s cluster"
elif gcloud container clusters get-credentials smartparent --region=us-central1 2>/dev/null; then
    echo "✅ Connected to smartparent cluster"
else
    echo "❌ Failed to connect to cluster"
    echo "Available clusters:"
    gcloud container clusters list
    exit 1
fi
echo ""

# Check current deployment status
echo "📊 Current TipTop Deployment Status:"
echo "===================================="
echo ""

echo "🔍 All pods in tiptop namespace:"
kubectl get pods -n tiptop
echo ""

echo "🔍 All services in tiptop namespace:"
kubectl get services -n tiptop
echo ""

echo "🔍 All ingress in tiptop namespace:"
kubectl get ingress -n tiptop
echo ""

# Focus on WebSocket server diagnostics
echo "🔌 WebSocket Server Detailed Diagnostics:"
echo "========================================="
echo ""

echo "📊 WebSocket deployment status:"
kubectl get deployment tiptop-websocket -n tiptop -o wide
echo ""

echo "📊 WebSocket pods detailed status:"
kubectl get pods -n tiptop -l app=tiptop-websocket -o wide
echo ""

echo "📊 WebSocket service details:"
kubectl get service tiptop-websocket -n tiptop -o wide
echo ""

echo "📊 WebSocket ingress details:"
kubectl get ingress tiptop-websocket-ingress -n tiptop -o wide 2>/dev/null || echo "❌ WebSocket ingress not found"
echo ""

# Check WebSocket logs for errors
echo "📋 WebSocket Server Logs (last 50 lines):"
echo "=========================================="
kubectl logs -n tiptop -l app=tiptop-websocket --tail=50 --prefix=true
echo ""

# Check for recent events
echo "📊 Recent Kubernetes Events:"
echo "============================"
kubectl get events -n tiptop --sort-by='.lastTimestamp' | tail -20
echo ""

# Test WebSocket connectivity
echo "🔍 Testing WebSocket Connectivity:"
echo "=================================="

# Get ingress IP
INGRESS_IP=$(kubectl get ingress tiptop-websocket-ingress -n tiptop -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
if [ -n "$INGRESS_IP" ]; then
    echo "✅ WebSocket ingress IP: $INGRESS_IP"
    echo "🔗 WebSocket should be accessible at: wss://ws.tiptop.qubitrhythm.com"
else
    echo "⚠️  WebSocket ingress IP not assigned yet"
fi

# Check service endpoints
echo ""
echo "📊 WebSocket service endpoints:"
kubectl get endpoints tiptop-websocket -n tiptop
echo ""

# Test internal connectivity
echo "🔍 Testing internal WebSocket connectivity:"
echo "kubectl run test-websocket --rm -i --tty --image=curlimages/curl -- curl -I http://tiptop-websocket.tiptop.svc.cluster.local:8080/health"
kubectl run test-websocket --rm -i --tty --image=curlimages/curl --restart=Never -- curl -I http://tiptop-websocket.tiptop.svc.cluster.local:8080/health 2>/dev/null || echo "❌ Internal connectivity test failed"
echo ""

# Check database connectivity and social tables
echo "🗄️  Database Diagnostics:"
echo "========================="
echo ""

echo "📊 PostgreSQL pod status:"
kubectl get pods -n tiptop -l app=tiptop-postgres
echo ""

echo "🔍 Checking database tables (especially social-related):"
kubectl exec -n tiptop deployment/tiptop-postgres -- psql -U postgres -d tiptop -c "\\dt" 2>/dev/null || echo "❌ Could not connect to database"
echo ""

echo "🔍 Checking social tables specifically:"
kubectl exec -n tiptop deployment/tiptop-postgres -- psql -U postgres -d tiptop -c "SELECT table_name FROM information_schema.tables WHERE table_name LIKE '%social%' OR table_name LIKE '%message%' OR table_name LIKE '%user%';" 2>/dev/null || echo "❌ Could not query social tables"
echo ""

echo "🔍 Checking recent social activity (if tables exist):"
kubectl exec -n tiptop deployment/tiptop-postgres -- psql -U postgres -d tiptop -c "SELECT COUNT(*) as message_count FROM messages;" 2>/dev/null || echo "❌ Messages table not found or accessible"
kubectl exec -n tiptop deployment/tiptop-postgres -- psql -U postgres -d tiptop -c "SELECT COUNT(*) as user_count FROM user_sessions;" 2>/dev/null || echo "❌ User sessions table not found or accessible"
echo ""

# PostgreSQL access command
echo "🗄️  PostgreSQL Access Command:"
echo "=============================="
echo ""
echo "To access the PostgreSQL database, use this one-liner:"
echo ""
echo "kubectl exec -it -n tiptop deployment/tiptop-postgres -- psql -U postgres -d tiptop"
echo ""

# DNS and connectivity checks
echo "🌐 DNS and Connectivity Checks:"
echo "==============================="
echo ""

echo "🔍 Checking DNS resolution for WebSocket endpoint:"
nslookup ws.tiptop.qubitrhythm.com || echo "❌ DNS resolution failed for ws.tiptop.qubitrhythm.com"
echo ""

echo "🔍 Checking SSL certificate for WebSocket endpoint:"
echo | openssl s_client -connect ws.tiptop.qubitrhythm.com:443 -servername ws.tiptop.qubitrhythm.com 2>/dev/null | openssl x509 -noout -dates 2>/dev/null || echo "❌ SSL certificate check failed"
echo ""

# Final recommendations
echo "🎯 Diagnostic Summary and Next Steps:"
echo "====================================="
echo ""
echo "✅ WebSocket server pods are running (as confirmed)"
echo ""
echo "🔍 Key things to check:"
echo "1. WebSocket logs for connection errors or rate limiting"
echo "2. Database connectivity and social tables"
echo "3. DNS resolution and SSL certificates"
echo "4. Extension configuration (production vs test mode)"
echo ""
echo "🔧 If social features still don't work:"
echo "1. Check browser console for WebSocket connection errors"
echo "2. Verify extension is using wss://ws.tiptop.qubitrhythm.com"
echo "3. Check if social toggle is enabled in extension"
echo "4. Monitor WebSocket logs in real-time:"
echo "   kubectl logs -n tiptop -l app=tiptop-websocket -f"
echo ""
echo "✅ Diagnostic script completed!"
