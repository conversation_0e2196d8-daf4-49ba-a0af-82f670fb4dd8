# Create the tiptop.qubitrhythm.com zone in GCP Cloud DNS and the related settings

1.  **Go to the Cloud DNS page in the Google Cloud Console:**
    [https://console.cloud.google.com/net-services/dns/zones](https://console.cloud.google.com/net-services/dns/zones)

2.  **Create a new Cloud DNS zone:**
    Click the "Create zone" button.

3.  **Configure the new zone:**
    *   **Zone name:** Enter `tiptop-dns` (or any other name you prefer).
    *   **DNS name:** Enter `tiptop.qubitrhythm.com`.
    *   **Description:** Enter a description for the zone (e.g., "DNS zone for TipTop").
    *   **DNSSEC:** Choose "Off" for now. You can enable DNSSEC later if you want to add an extra layer of security.
    *   **Zone type:** Choose "Public".

4.  **Create the zone:**
    Click the "Create" button.

5.  **Get the NS records for the new zone:**
    After the zone is created, you will see a list of NS records. These records point to the GCP Cloud DNS nameservers that are responsible for managing the `tiptop.qubitrhythm.com` zone. The NS records will look something like this:
    *   `ns-cloud-a1.googledomains.com.`
    *   `ns-cloud-a2.googledomains.com.`
    *   `ns-cloud-a3.googledomains.com.`
    *   `ns-cloud-a4.googledomains.com.`

6.  **Delegate the subdomain in GoDaddy:**
    *   Go to the GoDaddy website ([https://www.godaddy.com/](https://www.godaddy.com/)) and log in to your account.
    *   Find the domain you want to manage (in this case, `qubitrhythm.com`).
    *   Click the "DNS" button or navigate to the DNS management page for your domain.
    *   Create NS records for `tiptop.qubitrhythm.com` pointing to the GCP Cloud DNS nameservers that you obtained in step 5.
        *   **Host:** `tiptop`
        *   **Points to:** (The GCP Cloud DNS nameserver, e.g., `ns-cloud-a1.googledomains.com.`)
        *   **TTL:** (Choose a reasonable TTL, e.g., 1 hour)
    *   Repeat this process for all four GCP Cloud DNS nameservers.

7.  **Wait for DNS propagation:**
    It may take some time (up to 48 hours, but typically much less) for the DNS changes to propagate across the internet. You can use online tools like [https://www.whatsmydns.net/](https://www.whatsmydns.net/) to check the DNS propagation status.

8.  **Create the A record in GCP Cloud DNS:**
    *   After DNS propagation, go back to the Cloud DNS page in the Google Cloud Console.
    *   Select the `tiptop-dns` zone.
    *   Add a new A record.
    *   Configure the A record:
        *   **DNS name:** Leave this blank (or enter `@`). This will create the A record for `tiptop.qubitrhythm.com`.
        *   **Record type:** Select `A`.
        *   **TTL:** Choose a TTL (Time To Live) value. A common value is 300 seconds (5 minutes).
        *   **IPv4 Address:** Enter the IP address of your GCE Load Balancer. You will get this IP address after you deploy the Cloud Function and the Ingress is configured.
    *   Create the record.
