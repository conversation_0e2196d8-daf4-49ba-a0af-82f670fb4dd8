/**
 * Special handler for Wikipedia articles
 * This module provides optimized processing for Wikipedia articles,
 * which are known to be extremely large and cause issues with the standard processing.
 */

const axios = require('axios');
const { JSDOM } = require('jsdom');
const { Readability } = require('@mozilla/readability');

/**
 * Handle a Wikipedia article with special processing
 * @param {string} url - The Wikipedia article URL
 * @param {object} res - Express response object
 * @param {number} startTime - Request start time for timing
 * @returns {Promise} - Resolves when processing is complete
 */
async function handleWikipediaArticle(url, res, startTime) {
  console.log(`Using specialized Wikipedia handler for: ${url}`);

  try {
    // Extract the article title from the URL
    const articleTitle = url.split('/wiki/')[1].split('#')[0].replace(/_/g, ' ');
    console.log(`Extracted Wikipedia article title: ${articleTitle}`);

    // 1. Fetch multiple sections of the Wikipedia article
    const articleHtml = await fetchWikipediaContent(url);
    if (!articleHtml) {
      throw new Error('Failed to fetch Wikipedia content');
    }

    // 2. Extract section headings to understand the structure
    const sectionHeadings = await extractWikipediaSections(url);

    // 3. Process the article content with Readability
    const dom = new JSDOM(articleHtml, { url });
    const reader = new Readability(dom.window.document, {
      debug: false,
      maxElemsToParse: 10000, // Increased to handle more content
      nbTopCandidates: 5,
      charThreshold: 500
    });

    const article = reader.parse();
    if (!article || !article.textContent) {
      throw new Error('Failed to extract readable content from Wikipedia article');
    }

    // 4. Create a combined text for processing
    let processedText = article.textContent;

    // Add section headings to the processed text
    if (sectionHeadings && sectionHeadings.length > 0) {
      processedText += "\n\nThis Wikipedia article contains the following sections:\n" +
                      sectionHeadings.map(h => `- ${h}`).join('\n');
    }

    // 5. Generate a comprehensive summary based on multiple sections
    const summaryText = await generateWikipediaSummary(processedText, articleTitle);

    // 6. Create a response with appropriate messaging
    const responseTime = Date.now() - startTime;
    const responsePayload = {
      summary: {
        text: summaryText, // No additional note needed as we're now providing a comprehensive summary
        source: "Grok AI (Wikipedia Mode)"
      },
      // No key takeaways for Wikipedia articles - they're too general
      keyTakeaways: [],
      responseTime: responseTime,
      fromCache: false,
      isPartialResponse: false,
      pendingResources: false,
      pendingAffiliateResources: false,
      requestId: Date.now().toString(),
      isWikipedia: true,
      // Add Wikipedia-specific resources
      resources: generateWikipediaResources(articleTitle),
      affiliateResources: generateWikipediaAffiliateResources(articleTitle)
    };

    // Return the response
    return res.json(responsePayload);
  } catch (error) {
    console.error('Error in Wikipedia handler:', error);
    throw error; // Let the main handler deal with it
  }
}

/**
 * Fetch content from a Wikipedia article
 * @param {string} url - Wikipedia article URL
 * @returns {Promise<string>} - HTML content of the article
 */
async function fetchWikipediaContent(url) {
  try {
    // Use the Wikipedia API to get the full article HTML
    const apiUrl = url.replace('/wiki/', '/api/rest_v1/page/html/');
    const response = await axios.get(apiUrl, {
      timeout: 15000, // Increased timeout for larger content
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; TipTopBot/1.0; +https://tiptop.qubitrhythm.com)'
      }
    });

    // Extract the main content sections (introduction + first few main sections)
    const dom = new JSDOM(response.data);
    const mainContent = dom.window.document.querySelector('body');

    // Get the introduction and first few main sections
    const introSection = dom.window.document.querySelector('section[data-mw-section-id="0"]');
    const mainSections = Array.from(dom.window.document.querySelectorAll('section[data-mw-section-id]'))
      .filter(section => {
        // Get sections with IDs 0-5 (intro + first few main sections)
        const sectionId = parseInt(section.getAttribute('data-mw-section-id'));
        return sectionId >= 0 && sectionId <= 5;
      });

    if (mainSections.length > 0) {
      // Combine the selected sections into a single HTML string
      return mainSections.map(section => section.outerHTML).join('');
    } else if (mainContent) {
      // Fallback: take a larger portion of the content (up to 100KB)
      return mainContent.outerHTML.substring(0, 100000);
    } else {
      // Fallback: take the first 100KB of the raw HTML
      return response.data.substring(0, 100000);
    }
  } catch (error) {
    console.error('Error fetching Wikipedia content:', error);

    // Fallback: try to fetch the regular page and extract a larger portion
    try {
      const response = await axios.get(url, {
        timeout: 15000, // Increased timeout
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; TipTopBot/1.0; +https://tiptop.qubitrhythm.com)'
        }
      });

      // Return a larger portion of the HTML (up to 100KB)
      return response.data.substring(0, 100000);
    } catch (fallbackError) {
      console.error('Fallback Wikipedia fetch also failed:', fallbackError);
      return null;
    }
  }
}

/**
 * Extract section headings from a Wikipedia article
 * @param {string} url - Wikipedia article URL
 * @returns {Promise<string[]>} - Array of section headings
 */
async function extractWikipediaSections(url) {
  try {
    // Convert wiki URL to API URL for sections
    const baseUrl = url.split('/wiki/')[0];
    const title = url.split('/wiki/')[1].split('#')[0];
    const apiUrl = `${baseUrl}/w/api.php?action=parse&page=${title}&prop=sections&format=json&origin=*`;

    const response = await axios.get(apiUrl, {
      timeout: 5000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; TipTopBot/1.0; +https://tiptop.qubitrhythm.com)'
      }
    });

    if (response.data && response.data.parse && response.data.parse.sections) {
      // Filter out subsections (level > 2) and extract just the headings
      return response.data.parse.sections
        .filter(section => section.level <= 2)
        .map(section => section.line)
        .slice(0, 15); // Limit to 15 main sections
    }

    return [];
  } catch (error) {
    console.error('Error extracting Wikipedia sections:', error);
    return [];
  }
}

/**
 * Generate a comprehensive summary for Wikipedia articles
 * @param {string} text - Processed text (multiple sections + section headings)
 * @param {string} title - Article title
 * @returns {Promise<string>} - Generated summary
 */
async function generateWikipediaSummary(text, title) {
  const axios = require('axios');
  const config = require('./config');

  try {
    // Call the Grok API with an improved prompt for comprehensive Wikipedia summaries
    const response = await axios.post(config.grokApiUrl, {
      model: config.grokModel,
      messages: [
        {
          role: "system",
          content: "You are an AI assistant that creates comprehensive, informative summaries of Wikipedia articles. Analyze the provided content thoroughly and create a detailed summary that covers the main aspects of the topic. Your summary should be 3-4 paragraphs long, covering the most important information from all available sections, not just the introduction. Include key facts, historical context, significance, and major developments related to the topic. Make the summary engaging and educational. IMPORTANT: Always complete your sentences and paragraphs. Never end mid-sentence or with incomplete thoughts. If you're approaching the token limit, wrap up your current paragraph with a complete sentence rather than getting cut off."
        },
        {
          role: "user",
          content: `This is a Wikipedia article about ${title}. Please create a comprehensive summary based on the following content and section headings. Make sure your summary is complete with properly finished sentences and paragraphs:\n\n${text}`
        }
      ],
      temperature: 0.3, // Lower temperature for more factual output
      max_tokens: 800, // Optimal token limit for 3-4 paragraphs
      stop: null, // No explicit stop sequence, let the model complete its thoughts
      top_p: 0.95 // Slightly reduce randomness to favor more predictable completions
    }, {
      headers: {
        'Authorization': `Bearer ${config.grokApiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 20000 // Increased timeout for processing larger content
    });

    // Get the generated summary
    let summary = response.data.choices[0].message.content;

    // Check if the summary appears to be truncated (ends with incomplete sentence)
    const lastChar = summary.charAt(summary.length - 1);
    const lastTwoChars = summary.slice(-2);
    const lastThreeChars = summary.slice(-3);

    // If the summary doesn't end with proper punctuation, it might be truncated
    if (
      !lastChar.match(/[.!?]/) || // Doesn't end with sentence-ending punctuation
      lastTwoChars === '..' || // Ends with incomplete ellipsis
      (lastChar === '.' && !lastThreeChars.match(/[.]{3}/)) // Ends with period but not proper ellipsis
    ) {
      // Find the last complete sentence
      const sentenceRegex = /[^.!?]*[.!?]/g;
      const sentences = summary.match(sentenceRegex) || [];

      if (sentences.length > 0) {
        // Reconstruct the summary using only complete sentences
        summary = sentences.join('');
        console.log('Truncated summary detected, adjusted to end with complete sentence');
      }
    }

    return summary;
  } catch (error) {
    console.error('Error generating Wikipedia summary:', error);
    // Improved fallback summary if the API call fails
    const sections = text.split('- ').slice(1, 8);
    const sectionText = sections.length > 0 ? `and includes sections on ${sections.join(', ')}` : '';
    return `This is a Wikipedia article about ${title}. The article covers comprehensive information about this topic ${sectionText}. Wikipedia provides reliable, community-edited information on a wide range of topics. For a more detailed summary, please try again later when our service is fully operational.`;
  }
}

/**
 * Generate Wikipedia-specific resources
 * @param {string} title - Article title
 * @returns {Array} - Array of resource objects
 */
function generateWikipediaResources(title) {
  return [
    {
      title: `${title} on Encyclopedia Britannica`,
      description: `Compare this Wikipedia article with Encyclopedia Britannica's coverage of ${title}.`,
      url: `https://www.britannica.com/search?query=${encodeURIComponent(title)}`
    },
    {
      title: `${title} on Stanford Encyclopedia of Philosophy`,
      description: `Explore academic perspectives on ${title} from Stanford's renowned philosophy resource.`,
      url: `https://plato.stanford.edu/search/searcher.py?query=${encodeURIComponent(title)}`
    },
    {
      title: `${title} on Google Scholar`,
      description: `Find academic papers and research related to ${title}.`,
      url: `https://scholar.google.com/scholar?q=${encodeURIComponent(title)}`
    }
  ];
}

/**
 * Generate Wikipedia-specific affiliate resources
 * @param {string} title - Article title
 * @returns {Array} - Array of affiliate resource objects
 */
function generateWikipediaAffiliateResources(title) {
  return [
    {
      title: `Books about ${title}`,
      description: `Explore books related to ${title} on Amazon.`,
      url: `https://www.amazon.com/s?k=${encodeURIComponent(title + " book")}`,
      imageUrl: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjMWE3M2U4Ij48cGF0aCBkPSJNMTguOTIgNS45OEMxOC43MiA1Ljk4IDE4LjUgNi4wNiAxOC4zNCA2LjE0QzE4LjE4IDYuMjIgMTggNi4zNCAxNy44OCA2LjQ4QzE3Ljc2IDYuNjIgMTcuNjYgNi43OCAxNy42IDYuOTZDMTcuNTQgNy4xNCAxNy41IDcuMzIgMTcuNSA3LjVDMTcuNSA3LjY4IDE3LjU0IDcuODYgMTcuNiA4LjA0QzE3LjY2IDguMjIgMTcuNzYgOC4zOCAxNy44OCA4LjUyQzE4IDguNjYgMTguMTggOC43OCAxOC4zNCA4Ljg2QzE4LjUgOC45NCAxOC43MiA5IDE4LjkyIDlDMTkuMTIgOSAxOS4zIDguOTQgMTkuNDYgOC44NkMxOS42MiA4Ljc4IDE5LjggOC42NiAxOS45MiA4LjUyQzIwLjA0IDguMzggMjAuMTQgOC4yMiAyMC4yIDguMDRDMjAuMjYgNy44NiAyMC4zIDcuNjggMjAuMyA3LjVDMjAuMyA3LjMyIDIwLjI2IDcuMTQgMjAuMiA2Ljk2QzIwLjE0IDYuNzggMjAuMDQgNi42MiAxOS45MiA2LjQ4QzE5LjggNi4zNCAxOS42MiA2LjIyIDE5LjQ2IDYuMTRDMTkuMyA2LjA2IDE5LjEyIDUuOTggMTguOTIgNS45OE0zLjUgN1YyMUgxNy41VjdIM1VNMTkgN1YyMUgyMVY3SDE5TTguOTggOS43NEw4LjIzIDEwLjVMNy41IDkuNzZMNi45OCAxMC4yNkw3LjcyIDExTDYuOTggMTEuNzRMNy41IDEyLjI0TDguMjMgMTEuNUw4Ljk4IDEyLjI0TDkuNSAxMS43NEw4Ljc3IDExTDkuNSAxMC4yNkw4Ljk4IDkuNzRNMTUuOTggOS43NEwxNS4yMyAxMC41TDE0LjUgOS43NkwxMy45OCAxMC4yNkwxNC43MiAxMUwxMy45OCAxMS43NEwxNC41IDEyLjI0TDE1LjIzIDExLjVMMTUuOTggMTIuMjRMMTYuNSAxMS43NEwxNS43NyAxMUwxNi41IDEwLjI2TDE1Ljk4IDkuNzRNMTIuNSAxNC4wMUMxMS45OSAxNC4wMSAxMS41IDE0LjUgMTEuNSAxNS4wMVYxNkgxMy41VjE1LjAxQzEzLjUgMTQuNSAxMy4wMSAxNC4wMSAxMi41IDE0LjAxWiIvPjwvc3ZnPg=='
    },
    {
      title: `${title} Online Courses`,
      description: `Learn more about ${title} with online courses from leading educators.`,
      url: `https://www.udemy.com/courses/search/?q=${encodeURIComponent(title)}`,
      imageUrl: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjMWE3M2U4Ij48cGF0aCBkPSJNMTIsMkExMCwxMCAwIDAsMSAyMiwxMkExMCwxMCAwIDAsMSAxMiwyMkExMCwxMCAwIDAsMSAyLDEyQTEwLDEwIDAgMCwxIDEyLDJNMTIsMTZMMTgsMTBMMTYuNiw4LjZMMTIsMTMuMkw3LjQsOC42TDYsMTBMMTIsMTZaIi8+PC9zdmc+'
    }
  ];
}

module.exports = {
  handleWikipediaArticle
};
