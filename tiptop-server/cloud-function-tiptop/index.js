// index.js

const express = require('express');
const path = require('path');
const axios = require('axios');
const cors = require('cors');
const functions = require('@google-cloud/functions-framework');
const morgan = require('morgan');
const stripeModule = require('stripe');
const crypto = require('crypto');
const nodemailer = require('nodemailer');
const { JSDOM } = require('jsdom');
const { Readability } = require('@mozilla/readability');
const http = require('http');
const { handleWikipediaArticle } = require('./wikipedia-handler');
// Import new DB functions
const {
  pool,
  initializeDatabase,
  getInstallation,
  addInstallation,
  updateInstallationEmail,
  updateInstallationStatus,
  getInstallationsByEmail // Import the new function
} = require('./db');
// Import social feature modules
const socialDb = require('./social-db');
const socialApiRouter = require('./social-api');
const config = require('./config');
require('dotenv').config();

// Status Constants
const STATUS_TRIAL_PENDING_EMAIL = 'TrialPendingEmail';
const STATUS_TRIAL_ACTIVE = 'TrialActive';
const STATUS_TRIAL_EXPIRED = 'TrialExpired';
const STATUS_SUBSCRIBED = 'Subscribed';
const TRIAL_PERIOD_MS = 7 * 24 * 60 * 60 * 1000; // 7 days

// Load configurations from .env
const SERVER_URL = process.env.SERVER_URL;

// Add additional configuration to the imported config
config.timeout = process.env.TIMEOUT || 15000;
config.grokApiUrl = process.env.GROK_API_URL;
config.grokApiKey = process.env.GROK_API_KEY;
// The Grok model to use for API calls
// This can be changed by updating the GROK_MODEL environment variable in the cloud-function-secrets
// Available models include: grok-3-mini-fast-beta (fastest), grok-3-mini-beta, grok-2-latest
config.grokModel = process.env.GROK_MODEL || 'grok-3-mini-fast-beta'; // Default to grok-3-mini-fast-beta if not specified
config.stripeSecretKey = process.env.STRIPE_SECRET_KEY;
config.stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
config.port = process.env.PORT || 8080;
config.planPriceIdsStandard = process.env.PLAN_PRICE_IDS_STANDARD;
config.maxRetries = 1; // Default retries for most functions
config.summarizeMaxRetries = 1; // Keep retries for summary (most important)
config.keywordsMaxRetries = 1; // Keep retries for keywords (important)
config.takeawaysMaxRetries = 0; // No retries for takeaways (less important)
config.affiliateMaxRetries = 0; // No retries for affiliate resources (least important)
config.tipsLinksMaxRetries = 0; // No retries for tips/links (less important)
config.retryDelay = 500; // Reduced delay between retries to 500ms
// Cache settings
config.enableCache = true; // Enable caching of responses
config.cacheTTL = 3600000; // Cache time-to-live in milliseconds (1 hour)

// Validate essential environment variables
if (!config.stripeSecretKey) {
  throw new Error('STRIPE_SECRET_KEY environment variable is not set');
}
if (!config.stripeWebhookSecret) {
  throw new Error('STRIPE_WEBHOOK_SECRET environment variable is not set');
}
if (!SERVER_URL) {
  throw new Error('SERVER_URL environment variable is not set');
}
if (!config.grokApiUrl) {
  throw new Error('GROK_API_URL environment variable is not set');
}
if (!config.grokApiKey) {
  throw new Error('GROK_API_KEY environment variable is not set');
}

// Validate Grok model configuration
const validGrokModels = ['grok-3-mini-fast-beta', 'grok-3-mini-beta', 'grok-2-latest'];
if (!validGrokModels.includes(config.grokModel)) {
  console.warn(`Warning: GROK_MODEL '${config.grokModel}' is not in the list of known models: ${validGrokModels.join(', ')}. Using it anyway, but this may cause errors.`);
}

// Log configuration (without sensitive values)
console.log('TipTop service configuration:', {
  timeout: config.timeout,
  grokApiUrl: config.grokApiUrl ? '[SET]' : '[NOT SET]',
  grokApiKey: config.grokApiKey ? '[SET]' : '[NOT SET]',
  grokModel: config.grokModel,
  enableCache: config.enableCache,
  cacheTTL: config.cacheTTL,
  maxRetries: config.maxRetries,
  port: config.port
});

// Initialize Stripe
const stripe = stripeModule(config.stripeSecretKey);

// Initialize PostgreSQL database
initializeDatabase().catch(err => {
  console.error('Failed to initialize database:', err);
  process.exit(1);
});

const app = express();

// Security middleware to block common probe attempts
app.use((req, res, next) => {
  // List of paths commonly probed by attackers
  const suspiciousPaths = [
    '/.env',
    '/docker/.env',
    '/.git',
    '/wp-admin',
    '/wp-login',
    '/administrator',
    '/admin',
    '/backup',
    '/db',
    '/.DS_Store',
    '/config.php',
    '/phpinfo.php',
    '/phpmyadmin'
  ];

  const path = req.path.toLowerCase();
  if (suspiciousPaths.some(blockedPath => path.includes(blockedPath))) {
    // Log security probe attempt
    console.error(`Security probe attempt blocked: ${req.method} ${req.path} from ${req.ip}`);
    // Return 403 instead of 404 to indicate we actively block these requests
    return res.status(403).json({
      error: 'Access denied'
    });
  }

  next();
});

// Enhanced CORS Configuration
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

// Add CORS pre-flight middleware
app.options('*', cors());

// Logging Middleware with enhanced health check filtering
app.use(morgan('combined', {
  skip: (req) => {
    // Skip logging for health check probes and /check endpoint
    const ua = (req.headers['user-agent'] || '').toLowerCase();
    const isHealthCheck = ua.includes('kube-probe') ||
                         req.path === '/check' ||
                         req.path === '/healthz' ||
                         req.path === '/ready';
    return isHealthCheck;
  }
}));

// Disable console.log for health check endpoints
const originalLog = console.log;
console.log = function() {
  const stack = new Error().stack;
  if (stack.includes('/check') || stack.includes('/healthz') || stack.includes('/ready')) {
    return;
  }
  originalLog.apply(console, arguments);
};

// Middleware to Parse JSON Bodies and Capture Raw Body for Webhooks
app.use(
  express.json({
    verify: (req, _, buf) => {
      req.rawBody = buf.toString();
    },
  })
);

// Serve Static Files
app.use(express.static(path.join(__dirname, 'public')));

// Serve staticHosting folder
app.use('/staticHosting', express.static(path.join(__dirname, 'public', 'staticHosting')));

// Root Route
app.get('/', (_, res) => {
  console.log('Received GET request at /');
  res.send('Server is running');
});

// Define PLAN_PRICE_IDS from environment variables
const PLAN_PRICE_IDS = {
  standard: config.planPriceIdsStandard,
};

// Verify Subscription Endpoint
app.post('/verify-subscription', async (req, res) => {
  try {
    const { email } = req.body;
    console.log('Verifying subscription for email:', email);

    if (!email) {
      return res.status(400).json({ error: 'Email is required.' });
    }

    // Check subscription status in Stripe
    const customers = await stripe.customers.list({
      email: email,
      limit: 1,
      expand: ['data.subscriptions']
    });

    let isSubscribed = false;
    let subscriptionDetails = null;

    if (customers.data.length > 0) {
      const customer = customers.data[0];
      console.log('Found customer:', customer.id);

      // Get all subscriptions for this customer
      const subscriptions = await stripe.subscriptions.list({
        customer: customer.id,
        status: 'all', // Check all statuses
        expand: ['data.plan']
      });

      console.log('Customer subscriptions:', subscriptions.data.map(sub => ({
        id: sub.id,
        status: sub.status,
        current_period_end: new Date(sub.current_period_end * 1000)
      })));

      // Check for an active or trialing subscription specifically
      const activeSubscription = subscriptions.data.find(
        sub => sub.status === 'active' || sub.status === 'trialing'
      );

      if (activeSubscription) {
        isSubscribed = true;
        subscriptionDetails = {
          status: activeSubscription.status,
          current_period_end: new Date(activeSubscription.current_period_end * 1000),
          plan: activeSubscription.plan?.nickname || 'Standard Plan'
        };
        console.log('Active subscription found:', subscriptionDetails);
      } else {
        console.log('No active or trialing subscription found for customer:', customer.id);
      }
    } else {
      console.log('No customer found for email:', email);
    }

    res.json({
      subscribed: isSubscribed,
      subscription: subscriptionDetails
    });
  } catch (error) {
    console.error('Error in /verify-subscription:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Endpoint to create a Stripe Checkout session
app.post('/create-checkout-session', async (req, res) => {
  try {
    const { email, plan } = req.body;

    if (!email || !plan) {
      return res.status(400).json({ error: 'Missing email or plan parameter' });
    }

    const priceId = PLAN_PRICE_IDS[plan];
    if (!priceId) {
      return res.status(400).json({ error: 'Invalid subscription plan selected' });
    }

    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'subscription',
      customer_email: email,
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: `${SERVER_URL}/staticHosting/success.html?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${SERVER_URL}/staticHosting/cancel.html`,
      billing_address_collection: 'auto',
      metadata: {
        email: email,
        plan: plan,
      },
    });

    res.json({ url: session.url });
    console.log(`Checkout session created for ${email}, plan: ${plan}`);
  } catch (error) {
    console.error('Error in /create-checkout-session:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});


// --- Helper Function for Stripe Subscription Check ---
/**
 * Checks if an email has an active Stripe subscription.
 * Includes timeout handling.
 * @param {string} email - The customer's email address.
 * @returns {Promise<{isSubscribed: boolean, subscriptionDetails: object|null}>}
 */
async function checkSubscription(email) {
  if (!email) {
    return { isSubscribed: false, subscriptionDetails: null };
  }

  console.log(`Checking Stripe subscription for: ${email}`);
  try {
    // Timeout promise
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Stripe API timeout')), config.timeout || 10000) // Default 10s timeout
    );

    // Stripe API call promise
    const stripePromise = async () => {
      const customers = await stripe.customers.list({
        email: email,
        limit: 1,
      });

      if (customers.data.length === 0) {
        console.log(`Stripe: No customer found for ${email}`);
        return { isSubscribed: false, subscriptionDetails: null };
      }

      const customer = customers.data[0];
      const subscriptions = await stripe.subscriptions.list({
        customer: customer.id,
        status: 'all', // Fetch all to potentially log inactive ones if needed
        expand: ['data.plan'],
      });

      const activeSubscription = subscriptions.data.find(
        sub => sub.status === 'active' || sub.status === 'trialing'
      );

      if (activeSubscription) {
        const details = {
          status: activeSubscription.status,
          current_period_end: new Date(activeSubscription.current_period_end * 1000),
          plan: activeSubscription.plan?.nickname || 'Standard Plan'
        };
        console.log(`Stripe: Active subscription found for ${email}`, details);
        return { isSubscribed: true, subscriptionDetails: details };
      } else {
        console.log(`Stripe: No active/trialing subscription found for ${email}`);
        return { isSubscribed: false, subscriptionDetails: null };
      }
    };

    // Race the promises
    return await Promise.race([stripePromise(), timeoutPromise]);

  } catch (error) {
    console.error(`Error checking Stripe subscription for ${email}:`, error);
    // Don't block user flow on Stripe error, assume not subscribed
    return { isSubscribed: false, subscriptionDetails: null };
  }
}


// Stripe webhook endpoint
app.post('/webhook', async (req, res) => {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = config.stripeWebhookSecret;

  let event;

  console.log('Received webhook:', req.rawBody);

  try {
    event = stripe.webhooks.constructEvent(req.rawBody, sig, endpointSecret);
    console.log('Webhook event constructed:', event.type);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // --- Helper function to update status for all installations linked to an email ---
  const updateStatusByEmail = async (email, newStatus) => {
    if (!email) {
      console.error(`Webhook: Cannot update status (${newStatus}) without an email.`);
      return;
    }
    try {
      const installations = await getInstallationsByEmail(email);
      if (installations.length === 0) {
        console.log(`Webhook: No installations found for email ${email} to update status to ${newStatus}.`);
        return;
      }
      console.log(`Webhook: Found ${installations.length} installation(s) for email ${email}. Updating status to ${newStatus}.`);
      // Update status for all found installations
      const updatePromises = installations.map(inst => updateInstallationStatus(inst.ip_hash, newStatus));
      await Promise.all(updatePromises);
      console.log(`Webhook: Successfully updated status to ${newStatus} for installations associated with ${email}.`);
    } catch (dbError) {
      console.error(`Webhook: Database error updating status to ${newStatus} for email ${email}:`, dbError);
      // Decide if we should throw or just log
    }
  };

  // --- Handle different event types ---
  let customerEmail = null;
  let customerId = null;
  const eventData = event.data.object;

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        console.log('Webhook: Handling checkout.session.completed');
        // Prefer email from metadata if available, fallback to customer details
        customerEmail = eventData.metadata?.email || eventData.customer_details?.email;
        if (customerEmail) {
          await updateStatusByEmail(customerEmail, STATUS_SUBSCRIBED);
        } else {
          console.error('Webhook: checkout.session.completed - Could not determine email from session metadata or customer details.');
        }
        break;

      case 'customer.subscription.created':
        console.log('Webhook: Handling customer.subscription.created');
        // Need to retrieve customer email using customer ID
        customerId = eventData.customer;
        if (customerId) {
          try {
            const customer = await stripe.customers.retrieve(customerId);
            customerEmail = customer.email;
            if (customerEmail) {
              await updateStatusByEmail(customerEmail, STATUS_SUBSCRIBED);
            } else {
               console.error(`Webhook: customer.subscription.created - Customer ${customerId} has no email.`);
            }
          } catch (stripeError) {
             console.error(`Webhook: customer.subscription.created - Error retrieving customer ${customerId}:`, stripeError);
          }
        } else {
           console.error('Webhook: customer.subscription.created - Missing customer ID in event data.');
        }
        break;

      case 'customer.subscription.updated':
        console.log('Webhook: Handling customer.subscription.updated');
        const subscriptionStatus = eventData.status;
        console.log(`Webhook: Subscription status is now ${subscriptionStatus}`);
        // Need customer email
        customerId = eventData.customer;
        if (customerId) {
           try {
             const customer = await stripe.customers.retrieve(customerId);
             customerEmail = customer.email;
             if (customerEmail) {
                // Check if the subscription is active
                if (subscriptionStatus === 'active' || subscriptionStatus === 'trialing') {
                  await updateStatusByEmail(customerEmail, STATUS_SUBSCRIBED);
                } else {
                  // Assume inactive if not active/trialing (e.g., canceled, unpaid, past_due)
                  console.log(`Webhook: Subscription for ${customerEmail} is inactive (${subscriptionStatus}). Setting status to TrialExpired.`);
                  await updateStatusByEmail(customerEmail, STATUS_TRIAL_EXPIRED);
                }
             } else {
                console.error(`Webhook: customer.subscription.updated - Customer ${customerId} has no email.`);
             }
           } catch (stripeError) {
              console.error(`Webhook: customer.subscription.updated - Error retrieving customer ${customerId}:`, stripeError);
           }
        } else {
            console.error('Webhook: customer.subscription.updated - Missing customer ID in event data.');
        }
        break;

      case 'customer.subscription.deleted':
      case 'invoice.payment_failed':
        console.log(`Webhook: Handling ${event.type}`);
        // Need customer email
        customerId = eventData.customer; // For subscription object
        if (!customerId && eventData.object === 'invoice') { // For invoice object
            customerId = eventData.customer;
        }

        if (customerId) {
           try {
             const customer = await stripe.customers.retrieve(customerId);
             customerEmail = customer.email;
             if (customerEmail) {
                console.log(`Webhook: Subscription inactive event (${event.type}) for ${customerEmail}. Setting status to TrialExpired.`);
                await updateStatusByEmail(customerEmail, STATUS_TRIAL_EXPIRED);
             } else {
                console.error(`Webhook: ${event.type} - Customer ${customerId} has no email.`);
             }
           } catch (stripeError) {
              console.error(`Webhook: ${event.type} - Error retrieving customer ${customerId}:`, stripeError);
           }
        } else {
            console.error(`Webhook: ${event.type} - Missing customer ID in event data.`);
        }
        break;

      case 'billing.alert.triggered':
        // Log but take no action on status
        console.log('Webhook: Received billing.alert.triggered. No status change needed.');
        break;

      default:
        console.log(`Webhook: Unhandled event type ${event.type}`);
    }
  } catch (error) {
      console.error(`Webhook: Error processing event ${event.type}:`, error);
      // Decide if we should return 500 to signal Stripe to retry (if applicable)
      // For now, just log and return 200 to acknowledge receipt.
  }

  // Return a 200 response to acknowledge receipt of the event
  res.status(200).end();
});

// Utility function for delay
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

// Analyze content using Grok API with retry mechanism
async function analyzeContent(url) {
  let lastError;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      console.log(`Attempt ${attempt + 1} to analyze content for URL: ${url}`);

      // Create a timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('API timeout')), config.timeout);
      });

      // Create the API call promise
      // Wait if needed to avoid rate limiting
      await apiCallTracker.waitIfNeeded();

      // Validate API configuration before making the call
      if (!config.grokApiUrl || !config.grokApiKey) {
        throw new Error('Missing Grok API configuration. Please check GROK_API_URL and GROK_API_KEY environment variables.');
      }

      // Log API request details (without sensitive info)
      console.log(`Making API request to ${config.grokApiUrl} with model ${config.grokModel}`);

      const apiPromise = axios.post(config.grokApiUrl, {
        messages: [
          {
            role: 'system',
            content: "You are an AI assistant. Analyze the following URL for inappropriate content. Respond **only** with a JSON object containing two boolean fields: 'is_unsafe' and 'contains_games'. For example: {\"is_unsafe\": true, \"contains_games\": false}. 'is_unsafe' should be true if the content includes violent, bloody, horror, or pornographic content. If not, but it contains games or entertainment contents (such as videos, animations, novels, pictures, etc), 'contains_games' should be true."
          },
          {
            role: 'user',
            content: `Analyze the following URL: ${url}`
          }
        ],
        model: config.grokModel,
        stream: false,
        temperature: 0
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.grokApiKey}`
        },
        timeout: config.timeout
      });

      // Race between timeout and API call
      const response = await Promise.race([apiPromise, timeoutPromise]);
      const content = response.data.choices[0].message.content.trim();
      console.log(`Raw AI response (attempt ${attempt + 1}):`, content);

      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonString = jsonMatch[0];
        const responseData = JSON.parse(jsonString);
        return {
          is_safe: !responseData.is_unsafe,
          contains_games: responseData.contains_games,
        };
      } else {
        throw new Error(`Invalid JSON response: ${content}`);
      }
    } catch (error) {
      lastError = error;
      console.error(`Attempt ${attempt + 1} failed:`, error);

      if (attempt < config.maxRetries) {
        console.log(`Waiting ${config.retryDelay}ms before retry...`);
        await delay(config.retryDelay);
      }
    }
  }

  console.error('All attempts failed. Last error:', lastError);
  throw new Error(`Failed to analyze content after ${config.maxRetries + 1} attempts: ${lastError.message}`);
}

// --- Authorization Middleware (Example - adapt as needed) ---
// This function determines the user's status and attaches it to the request.
// It will be used by endpoints needing authorization like /check.
async function determineUserStatus(req, _res, next) {
  try {
    const ip = (req.headers['x-forwarded-for'] || req.socket.remoteAddress || '').split(',')[0].trim();
    req.ip_hash = crypto.createHash('sha256').update(ip).digest('hex'); // Attach hash to request

    let installation = await getInstallation(req.ip_hash);
    let currentStatus = STATUS_TRIAL_EXPIRED; // Default to expired if unknown
    let isSubscribed = false;
    let userEmail = null;

    if (!installation) {
      // First contact, create record pending email
      installation = await addInstallation(req.ip_hash, STATUS_TRIAL_PENDING_EMAIL);
      currentStatus = STATUS_TRIAL_PENDING_EMAIL;
    } else {
      userEmail = installation.email; // Get email if exists

      // --- Prioritize DB Subscription Status ---
      if (installation.status === STATUS_SUBSCRIBED) {
          console.log(`DB status is Subscribed for ${req.ip_hash}. Skipping trial checks.`);
          currentStatus = STATUS_SUBSCRIBED;
          isSubscribed = true;
          // Optional: Verify with Stripe here if you want extra assurance, but trust the DB first.
          // const subCheck = await checkSubscription(userEmail);
          // if (!subCheck.isSubscribed) { /* Log potential inconsistency */ }
      } else {
          // --- Not Subscribed in DB, proceed with Trial/Expiry Logic ---
          const installDate = installation.install_timestamp;
          const now = new Date();
          const trialExpired = (now - installDate) > TRIAL_PERIOD_MS;

          if (!trialExpired) {
              // Still within trial period
              if (installation.email) {
                  currentStatus = STATUS_TRIAL_ACTIVE;
                  // Update status in DB if it was pending
                  if (installation.status === STATUS_TRIAL_PENDING_EMAIL) {
                      await updateInstallationStatus(req.ip_hash, STATUS_TRIAL_ACTIVE);
                  }
              } else {
                  currentStatus = STATUS_TRIAL_PENDING_EMAIL;
              }
          } else {
              // Trial period is over (and DB status wasn't 'Subscribed')
              if (installation.email) {
                  // Double-check Stripe in case DB status is lagging (e.g., webhook failed)
                  const subCheck = await checkSubscription(installation.email);
                  if (subCheck.isSubscribed) {
                      console.log(`Stripe check confirms subscription for ${req.ip_hash} despite DB status being ${installation.status}. Updating DB.`);
                      currentStatus = STATUS_SUBSCRIBED;
                      isSubscribed = true;
                      await updateInstallationStatus(req.ip_hash, STATUS_SUBSCRIBED); // Correct DB status
                  } else {
                      currentStatus = STATUS_TRIAL_EXPIRED;
                      // Ensure DB status reflects expiry if not already set
                      if (installation.status !== STATUS_TRIAL_EXPIRED) {
                          await updateInstallationStatus(req.ip_hash, STATUS_TRIAL_EXPIRED);
                      }
                  }
              } else {
                  // Trial expired, no email provided yet
                  currentStatus = STATUS_TRIAL_EXPIRED;
                  if (installation.status !== STATUS_TRIAL_EXPIRED) {
                      await updateInstallationStatus(req.ip_hash, STATUS_TRIAL_EXPIRED);
                  }
              }
          }
      }
    }

     // Calculate remaining trial milliseconds if applicable
     let remainingMs = 0;
     if (currentStatus === STATUS_TRIAL_ACTIVE && installation?.install_timestamp) { // Check if install_timestamp exists
         const installDate = installation.install_timestamp;
         const now = new Date();
         const trialEndDate = new Date(installDate.getTime() + TRIAL_PERIOD_MS);
         remainingMs = Math.max(0, trialEndDate.getTime() - now.getTime()); // Ensure non-negative
     }

     // Attach status info to the request object for downstream handlers
     req.userStatus = {
       status: currentStatus,
       subscribed: isSubscribed,
       email: userEmail,
       ip_hash: req.ip_hash,
       remainingMs: currentStatus === STATUS_TRIAL_ACTIVE ? remainingMs : undefined // Only include if trial active
     };
     console.log(`Status for ${req.ip_hash}:`, req.userStatus);
    next();

  } catch (err) {
    console.error('Error in determineUserStatus middleware:', err);
    // Don't block, but maybe return a default "denied" status? Or let endpoint handle?
    // For now, let the endpoint decide based on missing req.userStatus
     req.userStatus = { status: 'Error', subscribed: false, email: null, ip_hash: req.ip_hash };
     next(); // Allow endpoint to handle error status
  }
}


// Enhanced /check endpoint with authorization
app.post('/check', determineUserStatus, async (req, res) => { // Added determineUserStatus middleware
  try {
    console.log('req.body:', req.body);
    const url = req.body.url;
    const { status } = req.userStatus || { status: 'Error' }; // Get status from middleware

    console.log(`Received /check request for URL: ${url} from ${req.ip_hash} with status: ${status}`);

    // --- Authorization Check ---
    if (status !== STATUS_TRIAL_ACTIVE && status !== STATUS_SUBSCRIBED) {
      console.log(`Unauthorized /check attempt by ${req.ip_hash} (status: ${status})`);
      return res.status(403).json({ error: 'Access denied. Please activate your trial or subscribe.' });
    }
    // --- End Authorization Check ---

    if (!url) {
      return res.status(400).json({ error: 'URL is required.' });
    }

    try {
      const analysis = await analyzeContent(url);
      console.log('Analysis result:', analysis);
      res.json(analysis);
    } catch (error) {
      console.error('Analysis error:', error);
      // Send a more specific error response
      if (error.message.includes('timeout')) {
        res.status(504).json({ error: 'Analysis timed out. Please try again.' });
      } else {
        res.status(500).json({
          error: 'Analysis failed',
          details: error.message
        });
      }
    }
  } catch (error) {
    console.error('Error in /check:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Initialize nodemailer transporter globally
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: process.env.SMTP_PORT || 465,
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.FROM_EMAIL || '<EMAIL>',
    pass: process.env.EMAIL_PASSWORD,
  }
});

// Verify SMTP configuration on startup
transporter.verify()
  .then(() => console.log('SMTP configuration is correct'))
  .catch(err => console.error('SMTP configuration error:', err));

app.post('/send-password-email', async (req, res) => {
  try {
    const { email, password } = req.body;
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required.' });
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format.' });
    }

    await transporter.sendMail({
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: email,
      subject: 'Your Password for SmartParent',
      text: `Hello,

Your new password for SmartParent is: ${password}

Best regards,
SmartParent Support Team
`,
    });

    console.log(`Password email sent to ${email}`);
    res.json({ success: true });
  } catch (err) {
    console.error('Error sending password email:', err);
    res.status(500).json({ error: 'Failed to send email.' });
  }
});

// --- New Endpoints ---

// Simplified /log-install (now just ensures a record exists)
app.post('/log-install', async (req, res) => {
  try {
    const ip = (req.headers['x-forwarded-for'] || req.socket.remoteAddress || '').split(',')[0].trim();
    const hashedIP = crypto.createHash('sha256').update(ip).digest('hex');

    let installation = await getInstallation(hashedIP);

    if (!installation) {
      // Add with pending status, /status will handle the logic
      await addInstallation(hashedIP, STATUS_TRIAL_PENDING_EMAIL);
      console.log('Installation logged (new) for IP hash:', hashedIP);
    } else {
      console.log('Installation log request for existing IP hash:', hashedIP);
      // Optionally update 'updated_at' timestamp implicitly via trigger if needed
    }
    res.status(200).json({ success: true });
  } catch (err) {
    console.error('Error logging installation:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});


// Renamed /check-trial to /status and using determineUserStatus middleware
app.get('/status', determineUserStatus, async (req, res) => {
  try {
    // The determineUserStatus middleware already calculated the status
    // and attached it to req.userStatus
    if (!req.userStatus) {
         // Should not happen if middleware is working, but handle defensively
         console.error('User status not found in /status endpoint for IP hash:', req.ip_hash);
         return res.status(500).json({ error: 'Failed to determine user status.' });
    }

    console.log('Responding to /status for', req.ip_hash, 'with:', req.userStatus);
    res.json(req.userStatus);

  } catch (err) {
    // This catch block might be redundant if middleware handles errors,
    // but keep for safety.
    console.error('Error in /status endpoint:', err);
    res.status(500).json({ error: 'Internal server error checking status' });
  }
});


// New /activate endpoint
app.post('/activate', determineUserStatus, async (req, res) => {
  try {
    const { email } = req.body;
    const { status, ip_hash } = req.userStatus; // Get status determined by middleware

    if (!email) {
      return res.status(400).json({ error: 'Email is required.' });
    }
     // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format.' });
    }

    console.log(`Activation attempt for ${ip_hash} with email ${email}, current status: ${status}`);

    if (status === STATUS_TRIAL_PENDING_EMAIL || status === STATUS_TRIAL_ACTIVE) {
      // User is within the trial period (or was pending email)
      // Update email and set status to TrialActive
      await updateInstallationEmail(ip_hash, email);
      await updateInstallationStatus(ip_hash, STATUS_TRIAL_ACTIVE);
      console.log(`Activated trial for ${ip_hash} with email ${email}`);
      res.json({ status: STATUS_TRIAL_ACTIVE, subscribed: false, email: email });

    } else if (status === STATUS_SUBSCRIBED) {
        // Already subscribed (middleware confirmed via Stripe using existing email)
        // Maybe update email if different? Or just confirm status.
        // For now, just confirm they are subscribed.
        // If the provided email is different from the stored one, maybe update it?
        const installation = await getInstallation(ip_hash);
        if (installation && installation.email !== email) {
            console.log(`Updating email for already subscribed user ${ip_hash} from ${installation.email} to ${email}`);
            await updateInstallationEmail(ip_hash, email);
        }
        res.json({ status: STATUS_SUBSCRIBED, subscribed: true, email: email });

    } else if (status === STATUS_TRIAL_EXPIRED) {
      // Trial expired, check Stripe with the *provided* email now
      const subCheck = await checkSubscription(email);
      if (subCheck.isSubscribed) {
        // They are subscribed with this email, update DB and return subscribed status
        await updateInstallationEmail(ip_hash, email); // Update email if different
        await updateInstallationStatus(ip_hash, STATUS_SUBSCRIBED);
        console.log(`Activated subscription for ${ip_hash} via email ${email} after trial expired.`);
        res.json({ status: STATUS_SUBSCRIBED, subscribed: true, email: email });
      } else {
        // Trial expired and not subscribed with this email
        console.log(`Activation failed for ${ip_hash}, trial expired and email ${email} not subscribed.`);
        // Update the email in the DB anyway, so we know what they tried
        await updateInstallationEmail(ip_hash, email);
        res.json({ status: STATUS_TRIAL_EXPIRED, subscribed: false, email: email });
      }
    } else {
         // Handle unexpected status from middleware (e.g., 'Error')
         console.error(`Unexpected status '${status}' during activation for ${ip_hash}`);
         res.status(500).json({ error: 'Internal server error during activation.' });
    }

  } catch (err) {
    console.error('Error checking trial status:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Add new endpoint for sending history email
app.post('/send-history-email', async (req, res) => {
    try {
        const { email, history } = req.body;
        console.log('Received history email request for:', email);

        if (!email || !history) {
            console.error('Missing email or history data');
            return res.status(400).json({ error: 'Email and history are required.' });
        }

        const historyHTML = history.map(item => `
            <tr>
                <td style="padding: 8px; border: 1px solid #ddd;">${item.title || 'Untitled'}</td>
                <td style="padding: 8px; border: 1px solid #ddd;"><a href="${item.url}">${item.url}</a></td>
                <td style="padding: 8px; border: 1px solid #ddd;">${item.visitCount}</td>
                <td style="padding: 8px; border: 1px solid #ddd;">${formatTime(item.timeSpent)}</td>
            </tr>
        `).join('');

        const emailHTML = `
            <h2>Your Browsing History for ${new Date().toLocaleDateString()}</h2>
            <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                <tr style="background-color: #f8f9fa;">
                    <th style="padding: 12px; border: 1px solid #ddd;">Title</th>
                    <th style="padding: 12px; border: 1px solid #ddd;">URL</th>
                    <th style="padding: 12px; border: 1px solid #ddd;">Visits</th>
                    <th style="padding: 12px; border: 1px solid #ddd;">Time Spent</th>
                </tr>
                ${historyHTML}
            </table>
        `;

        await transporter.sendMail({
            from: process.env.FROM_EMAIL || '<EMAIL>',
            to: email,
            subject: "Today's browsing history",
            html: emailHTML
        });

        console.log('History email sent successfully to:', email);
        res.json({ success: true });
    } catch (err) {
        console.error('Error sending history email:', err);
        res.status(500).json({ error: 'Failed to send history email.' });
    }
});

// Helper function to format seconds into readable time
function formatTime(seconds) {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ${seconds % 60}s`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
}

// --- TipTop Endpoint Helper Functions ---

// Simple in-memory cache for TipTop responses
const responseCache = new Map();

// Helper function to validate URL format (basic check)
function isValidHttpUrl(string) {
  let url;
  try {
    url = new URL(string);
  } catch (_) {
    return false;
  }
  return url.protocol === "http:" || url.protocol === "https:";
}

// Track API calls to implement rate limiting
const apiCallTracker = {
  calls: [],
  maxCallsPerSecond: 9, // Keep under the 10 rps limit to be safe

  // Add a call to the tracker
  addCall() {
    const now = Date.now();
    this.calls.push(now);
    // Remove calls older than 1 second
    this.calls = this.calls.filter(time => now - time < 1000);
  },

  // Check if we're over the rate limit
  isOverLimit() {
    return this.calls.length >= this.maxCallsPerSecond;
  },

  // Get time to wait before making another call (in ms)
  getWaitTime() {
    if (!this.isOverLimit()) return 0;

    // Calculate when the oldest call will be more than 1 second old
    const oldestCall = this.calls[0];
    return Math.max(0, 1000 - (Date.now() - oldestCall));
  },

  // Wait if needed before making an API call
  async waitIfNeeded() {
    const waitTime = this.getWaitTime();
    if (waitTime > 0) {
      console.log(`Rate limit reached, waiting ${waitTime}ms before making another API call`);
      await delay(waitTime);
    }
    this.addCall();
  }
};

// Helper function to generate SVG data URLs for resource icons
function getResourceIconDataUrl(resourceType) {
  // Define SVG icons as data URLs for different resource types
  const icons = {
    // Book icon (open book)
    book: 'data:image/svg+xml;base64,' + Buffer.from(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="64" height="64" fill="#1a73e8"><path d="M21,4H3C2.4,4,2,4.4,2,5v14c0,0.6,0.4,1,1,1h18c0.6,0,1-0.4,1-1V5C22,4.4,21.6,4,21,4z M11,16.5c0,0.3-0.2,0.5-0.5,0.5h-5C5.2,17,5,16.8,5,16.5v-9C5,7.2,5.2,7,5.5,7h5C10.8,7,11,7.2,11,7.5V16.5z M19,16.5c0,0.3-0.2,0.5-0.5,0.5h-5c-0.3,0-0.5-0.2-0.5-0.5v-9C13,7.2,13.2,7,13.5,7h5C18.8,7,19,7.2,19,7.5V16.5z"/></svg>`).toString('base64'),

    // Course icon (graduation cap)
    course: 'data:image/svg+xml;base64,' + Buffer.from(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="64" height="64" fill="#4285f4"><path d="M12,3L1,9l4,2.2v6L12,21l7-3.8v-6l2-1.1V17h2V9L12,3z M12,5.1l7.1,3.9L12,12.9L4.9,9L12,5.1z M17,15.6l-5,2.7v-5.4l5-2.7V15.6z"/></svg>`).toString('base64'),

    // Tool icon (wrench)
    tool: 'data:image/svg+xml;base64,' + Buffer.from(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="64" height="64" fill="#ea4335"><path d="M22.7,19l-9.1-9.1c0.9-2.3,0.4-5-1.5-6.9c-2-2-5-2.4-7.4-1.3L9,6L6,9L1.6,4.7C0.4,7.1,0.9,10.1,2.9,12.1c1.9,1.9,4.6,2.4,6.9,1.5l9.1,9.1c0.4,0.4,1,0.4,1.4,0l2.3-2.3C23.1,20,23.1,19.3,22.7,19z"/></svg>`).toString('base64'),

    // Video icon (play button)
    video: 'data:image/svg+xml;base64,' + Buffer.from(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="64" height="64" fill="#34a853"><path d="M12,2C6.5,2,2,6.5,2,12s4.5,10,10,10s10-4.5,10-10S17.5,2,12,2z M10,16.5v-9l6,4.5L10,16.5z"/></svg>`).toString('base64'),

    // Default icon (info) - using blue style to match other icons
    default: 'data:image/svg+xml;base64,' + Buffer.from(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="64" height="64" fill="#1a73e8"><path d="M12,2C6.5,2,2,6.5,2,12s4.5,10,10,10s10-4.5,10-10S17.5,2,12,2z M13,17h-2v-6h2V17z M13,9h-2V7h2V9z"/></svg>`).toString('base64')
  };

  return icons[resourceType] || icons.default;
}

// Helper function to get cached response or null
function getCachedResponse(url) {
  if (!config.enableCache) return null;

  const cacheKey = url.toLowerCase();
  if (responseCache.has(cacheKey)) {
    const cachedItem = responseCache.get(cacheKey);
    // Check if cache is still valid
    if (Date.now() - cachedItem.timestamp < config.cacheTTL) {
      console.log(`Cache hit for URL: ${url}`);
      return cachedItem.data;
    } else {
      // Cache expired, remove it
      console.log(`Cache expired for URL: ${url}`);
      responseCache.delete(cacheKey);
    }
  }
  return null;
}

// Helper function to cache a response
function cacheResponse(url, data) {
  if (!config.enableCache) return;

  const cacheKey = url.toLowerCase();
  responseCache.set(cacheKey, {
    timestamp: Date.now(),
    data: data
  });

  // Limit cache size to prevent memory issues
  if (responseCache.size > 100) {
    // Remove oldest entry
    const oldestKey = responseCache.keys().next().value;
    responseCache.delete(oldestKey);
  }

  console.log(`Cached response for URL: ${url}`);
}

async function fetchAndParse(url) {
  try {
    console.log(`Fetching content from URL: ${url}`);

    // Validate URL format before making the request
    if (!isValidHttpUrl(url)) {
      throw new Error('Invalid URL format. URL must start with http:// or https://');
    }

    // Make the request with enhanced error handling
    let response;
    try {
      response = await axios.get(url, {
        timeout: config.timeout,
        maxContentLength: 10 * 1024 * 1024, // 10MB max content size
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; TipTopBot/1.0; +https://tiptop.qubitrhythm.com)'
        }
      });
    } catch (fetchError) {
      // Provide more specific error messages based on the error type
      if (fetchError.code === 'ECONNABORTED') {
        throw new Error(`Request timed out after ${config.timeout}ms. The page may be too large or slow to respond.`);
      } else if (fetchError.response) {
        // The request was made and the server responded with a status code outside of 2xx
        throw new Error(`Server returned error status: ${fetchError.response.status} ${fetchError.response.statusText}`);
      } else if (fetchError.request) {
        // The request was made but no response was received
        throw new Error('No response received from server. The site may be down or blocking our requests.');
      } else {
        // Something happened in setting up the request
        throw fetchError;
      }
    }

    // Check content type to ensure it's HTML
    const contentType = response.headers['content-type'] || '';
    if (!contentType.includes('text/html')) {
      console.warn(`URL returned non-HTML content type: ${contentType}`);
      // Continue anyway, but log the warning
    }

    // Check for extremely large HTML content
    const htmlSize = response.data.length;
    if (htmlSize > 5 * 1024 * 1024) { // 5MB
      console.warn(`HTML content is extremely large (${(htmlSize / (1024 * 1024)).toFixed(2)}MB), this may cause processing issues`);
      // For extremely large HTML, we'll truncate it to avoid memory issues
      const truncatedHtml = response.data.substring(0, 2 * 1024 * 1024); // Take first 2MB
      console.log(`Truncated HTML from ${(htmlSize / (1024 * 1024)).toFixed(2)}MB to 2MB for processing`);
      const dom = new JSDOM(truncatedHtml, { url });
      // Add a flag to indicate this content was truncated
      dom.window.document.wasTruncated = true;
      return processDOM(dom, url, true);
    }

    const dom = new JSDOM(response.data, { url });
    return processDOM(dom, url, false);
  } catch (error) {
    console.error(`Error fetching or parsing URL ${url}:`, error);
    throw new Error(`Failed to fetch or parse URL: ${error.message}`);
  }
}

// Helper function to process the DOM and extract content
function processDOM(dom, url, wasTruncated) {
  try {

    // Use Mozilla's Readability to extract the main content
    // This removes navigation, sidebars, ads, and other non-essential content
    let article;
    try {
      // If the HTML was truncated, use more aggressive settings
      const reader = new Readability(dom.window.document, {
        // Configure Readability for better content extraction
        debug: false,
        maxElemsToParse: wasTruncated ? 5000 : 10000, // Reduce parsing for truncated content
        nbTopCandidates: 5,
        charThreshold: 500
      });

      article = reader.parse();

      if (!article) {
        throw new Error('Readability could not parse any content from the page');
      }

      // Check if we got meaningful content
      if (!article.textContent || article.textContent.trim().length < 100) {
        throw new Error('Extracted content is too short or empty');
      }

      // Log content size for debugging
      console.log(`Extracted main content from ${url}: ${article.textContent.length} characters`);

      // Add a flag to indicate if the content was from truncated HTML
      if (wasTruncated) {
        article.wasTruncated = true;
        console.log('Article was extracted from truncated HTML content');
      }

      // Further optimize by truncating extremely long content
      if (article.textContent.length > 30000) {
        console.log(`Content is very long (${article.textContent.length} chars), truncating to 30000 chars`);
        article.textContent = article.textContent.substring(0, 30000) + "...";
      }
    } catch (readabilityError) {
      console.error(`Error extracting content with Readability: ${readabilityError.message}`);
      throw new Error(`Failed to extract readable content: ${readabilityError.message}`);
    }

    return article; // Contains title, content (HTML), textContent, length, excerpt, siteName
  } catch (error) {
    console.error(`Error fetching or parsing URL ${url}:`, error);
    throw new Error(`Failed to fetch or parse URL: ${error.message}`);
  }
}

// Summarize text using Grok API with retry and timeout
async function summarizeText(text) {
  if (!text || text.trim().length === 0) {
    console.log('Skipping summarization for empty text.');
    return "No content available for summarization.";
  }

  console.log(`Attempting to summarize text (length: ${text.length})`);
  let lastError;

  // More aggressive truncation for summarization to improve response time
  const MAX_TEXT_LENGTH = 10000; // Reduced from 15000 to 10000 characters

  // Extract the most important parts of the text for summarization
  let truncatedText;
  if (text.length > MAX_TEXT_LENGTH) {
    // Take the first 5000 characters (usually contains the most important info)
    const beginning = text.substring(0, 5000);
    // Take the middle 2500 characters
    const middleStart = Math.floor(text.length / 2) - 1250;
    const middle = text.substring(middleStart, middleStart + 2500);
    // Take the last 2500 characters
    const end = text.substring(text.length - 2500);
    // Combine with markers
    truncatedText = beginning + "\n[...content omitted...]\n" + middle + "\n[...content omitted...]\n" + end;
    console.log(`Text truncated from ${text.length} to approximately ${truncatedText.length} characters`);
  } else {
    truncatedText = text;
  }


  for (let attempt = 0; attempt <= config.summarizeMaxRetries; attempt++) {
    try {
      console.log(`Summarization attempt ${attempt + 1}`);

      // Create a timeout promise - use longer timeout for summary (most important feature)
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Grok API timeout during summarization')), config.timeout * 1.2);
      });

      // Create the API call promise
      // Wait if needed to avoid rate limiting
      await apiCallTracker.waitIfNeeded();

      // Validate API configuration before making the call
      if (!config.grokApiUrl || !config.grokApiKey) {
        throw new Error('Missing Grok API configuration. Please check GROK_API_URL and GROK_API_KEY environment variables.');
      }

      // Log API request details (without sensitive info)
      console.log(`Making API request to ${config.grokApiUrl} with model ${config.grokModel}`);

      const apiPromise = axios.post(config.grokApiUrl, {
        messages: [
          {
            role: 'system',
            content: "You are an AI assistant. Summarize the following text in a concise paragraph of 4-6 sentences. Do not use markdown formatting or section headers in your response."
          },
          {
            role: 'user',
            content: truncatedText // Send potentially truncated text
          }
        ],
        model: config.grokModel, // Using the model specified in environment variables
        stream: false,
        temperature: 0.3 // Lower temperature for faster, more deterministic responses
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.grokApiKey}`
        },
        timeout: config.timeout // Axios timeout
      });

      // Race between timeout and API call
      const response = await Promise.race([apiPromise, timeoutPromise]);

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const summary = response.data.choices[0].message.content.trim();
        console.log(`Summarization successful (attempt ${attempt + 1})`);
        return summary;
      } else {
        throw new Error('Invalid response structure from Grok API');
      }
    } catch (error) {
      lastError = error;
      console.error(`Summarization attempt ${attempt + 1} failed:`, error.message);

      if (attempt < config.summarizeMaxRetries) {
        console.log(`Waiting ${config.retryDelay}ms before retry...`);
        await delay(config.retryDelay);
      }
    }
  }

  console.error('All summarization attempts failed. Last error:', lastError?.message);
  // Return a user-friendly error message
  return "Could not generate summary at this time.";
}

// Extract keywords using Grok API
async function extractKeywords(text) {
  if (!text || text.trim().length === 0) {
    console.log('Skipping keyword extraction for empty text.');
    return [];
  }

  console.log(`Attempting to extract keywords (text length: ${text.length})`);
  let lastError;

  // Limit text length (adjust as needed)
  const MAX_TEXT_LENGTH = 15000;
  const truncatedText = text.length > MAX_TEXT_LENGTH ? text.substring(0, MAX_TEXT_LENGTH) + "..." : text;

  for (let attempt = 0; attempt <= config.keywordsMaxRetries; attempt++) {
    try {
      console.log(`Keyword extraction attempt ${attempt + 1}`);

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Grok API timeout during keyword extraction')), config.timeout * 0.9);
      });

      // Wait if needed to avoid rate limiting
      await apiCallTracker.waitIfNeeded();

      // Validate API configuration before making the call
      if (!config.grokApiUrl || !config.grokApiKey) {
        throw new Error('Missing Grok API configuration. Please check GROK_API_URL and GROK_API_KEY environment variables.');
      }

      // Log API request details (without sensitive info)
      console.log(`Making API request to ${config.grokApiUrl} with model ${config.grokModel}`);

      const apiPromise = axios.post(config.grokApiUrl, {
        messages: [
          {
            role: 'system',
            content: "You are an AI assistant. Extract the 5 most relevant keywords or key phrases from the following text. Respond **only** with a JSON array of strings, like [\"keyword1\", \"key phrase 2\", \"keyword3\"]."
          },
          {
            role: 'user',
            content: truncatedText
          }
        ],
        model: config.grokModel, // Using the model specified in environment variables
        stream: false,
        temperature: 0.2 // Lower temperature for more deterministic keyword extraction
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.grokApiKey}`
        },
        timeout: config.timeout
      });

      const response = await Promise.race([apiPromise, timeoutPromise]);

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const content = response.data.choices[0].message.content.trim();
        console.log(`Raw keyword response (attempt ${attempt + 1}):`, content);
        // Try to parse the JSON array directly
        try {
          const keywords = JSON.parse(content);
          if (Array.isArray(keywords) && keywords.every(k => typeof k === 'string')) {
            console.log(`Keyword extraction successful (attempt ${attempt + 1})`);
            return keywords.slice(0, 5); // Ensure max 5 keywords
          } else {
             throw new Error('Parsed response is not a string array.');
          }
        } catch (parseError) {
          console.error(`Failed to parse keywords JSON (attempt ${attempt + 1}):`, parseError);
          throw new Error(`Invalid JSON response for keywords: ${content}`);
        }
      } else {
        throw new Error('Invalid response structure from Grok API for keywords');
      }
    } catch (error) {
      lastError = error;
      console.error(`Keyword extraction attempt ${attempt + 1} failed:`, error.message);

      if (attempt < config.keywordsMaxRetries) {
        console.log(`Waiting ${config.retryDelay}ms before retry...`);
        await delay(config.retryDelay);
      }
    }
  }

  console.error('All keyword extraction attempts failed. Last error:', lastError?.message);
  // Return empty array on failure
  return [];
}

// Extract key takeaways using Grok API
async function extractKeyTakeaways(text, keywords) {
  if (!text || text.trim().length === 0) {
    console.log('Skipping key takeaways extraction for empty text.');
    return [];
  }

  console.log(`Attempting to extract key takeaways (text length: ${text.length})`);
  let lastError;

  // Limit text length (adjust as needed)
  const MAX_TEXT_LENGTH = 15000;
  const truncatedText = text.length > MAX_TEXT_LENGTH ? text.substring(0, MAX_TEXT_LENGTH) + "..." : text;
  const keywordsString = keywords.join(', ');

  for (let attempt = 0; attempt <= config.takeawaysMaxRetries; attempt++) {
    try {
      console.log(`Key takeaways extraction attempt ${attempt + 1}`);

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Grok API timeout during key takeaways extraction')), config.timeout * 0.8);
      });

      // Wait if needed to avoid rate limiting
      await apiCallTracker.waitIfNeeded();

      // Validate API configuration before making the call
      if (!config.grokApiUrl || !config.grokApiKey) {
        throw new Error('Missing Grok API configuration. Please check GROK_API_URL and GROK_API_KEY environment variables.');
      }

      // Log API request details (without sensitive info)
      console.log(`Making API request to ${config.grokApiUrl} with model ${config.grokModel}`);

      const apiPromise = axios.post(config.grokApiUrl, {
        messages: [
          {
            role: 'system',
            content: "You are an AI assistant. Extract 2-3 HIGHLY SPECIFIC key insights from the following text. These should be the most valuable, non-obvious points that would be useful to someone reading this content. Focus on unique information, surprising facts, or expert insights related to these keywords: " + keywordsString + ". DO NOT provide generic advice like 'research more' or 'compare sources'. Instead, extract actual specific knowledge from the text that would be valuable to remember. Each takeaway should contain concrete information unique to this content. If you cannot find specific, valuable insights, return fewer takeaways or none at all rather than providing generic ones."
          },
          {
            role: 'user',
            content: truncatedText
          }
        ],
        model: config.grokModel, // Using the model specified in environment variables
        stream: false,
        temperature: 0.3 // Lower temperature for more deterministic extraction
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.grokApiKey}`
        },
        timeout: config.timeout
      });

      const response = await Promise.race([apiPromise, timeoutPromise]);

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const content = response.data.choices[0].message.content.trim();
        console.log(`Raw key takeaways response (attempt ${attempt + 1}):`, content);

        // Split the content by newlines and filter out empty lines
        const takeaways = content.split('\n')
          .map(line => line.trim())
          .filter(line => line.length > 0)
          .map(line => {
            // Remove numbering if present (e.g., "1. IMPORTANT: ...")
            let processed = line.replace(/^\d+\.\s*/, '');
            // Remove category prefixes if present (e.g., "IMPORTANT: ...")
            processed = processed.replace(/^[A-Z]+:\s*/, '');
            // Ensure the first letter is capitalized
            if (processed.length > 0) {
              processed = processed.charAt(0).toUpperCase() + processed.slice(1);
            }
            return processed;
          })
          .filter(line => line.length >= 15) // Ensure takeaways are substantial and specific
          .filter(line => {
            // Filter out generic advice that doesn't contain specific information
            const genericPhrases = [
              'research more',
              'compare sources',
              'explore further',
              'learn more',
              'consider different',
              'understand the',
              'be aware of',
              'keep in mind',
              'remember that',
              'it is important to'
            ];

            // Check if the takeaway contains any generic phrases
            const isGeneric = genericPhrases.some(phrase =>
              line.toLowerCase().includes(phrase)
            );

            // Only keep non-generic takeaways
            return !isGeneric;
          })
          .slice(0, 3); // Limit to 3 takeaways

        console.log(`Key takeaways extraction successful (attempt ${attempt + 1})`);
        return takeaways;
      } else {
        throw new Error('Invalid response structure from Grok API for key takeaways');
      }
    } catch (error) {
      lastError = error;
      console.error(`Key takeaways extraction attempt ${attempt + 1} failed:`, error.message);

      if (attempt < config.takeawaysMaxRetries) {
        console.log(`Waiting ${config.retryDelay}ms before retry...`);
        await delay(config.retryDelay);
      }
    }
  }

  console.error('All key takeaways extraction attempts failed. Last error:', lastError?.message);
  // Return empty array on failure
  return [];
}

// Generate affiliate resources based on keywords
async function generateAffiliateResources(keywords) {
  if (!keywords || keywords.length === 0) {
    console.log('Skipping affiliate resources generation due to no keywords.');
    return [];
  }

  console.log('Generating affiliate resources for keywords:', keywords);
  let lastError;
  const keywordsString = keywords.join(', ');

  for (let attempt = 0; attempt <= config.affiliateMaxRetries; attempt++) {
    try {
      console.log(`Affiliate resources generation attempt ${attempt + 1}`);

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Grok API timeout during affiliate resources generation')), config.timeout * 0.7);
      });

      const apiPrompt = `Given the keywords: "${keywordsString}", generate exactly 2 specific, real product recommendations that would be genuinely useful for someone interested in these topics.

Provide REAL products with ACCURATE information - not generic placeholders. Include:
- Specific product names from real brands/companies
- Detailed descriptions explaining why this exact product is valuable for this topic
- Realistic current prices (research current pricing if possible)
- Actual product URLs from reputable retailers (Amazon, specialized stores, etc.)

Respond **only** with a valid JSON array following this exact structure:
[
  {
    "title": "Specific Product Name by Brand",
    "description": "Detailed description of why this specific product is valuable (2-3 sentences)",
    "price": "$XX.XX",
    "url": "https://retailer.com/actual-product-page",
    "imageUrl": "https://retailer.com/product-image.jpg"
  }
]

Note: The imageUrl will be replaced with an icon, so focus on providing accurate product information rather than finding perfect images. Ensure all URLs are valid and start with http:// or https://. Use real product URLs from major retailers when possible.`;

      // Wait if needed to avoid rate limiting
      await apiCallTracker.waitIfNeeded();

      // Validate API configuration before making the call
      if (!config.grokApiUrl || !config.grokApiKey) {
        throw new Error('Missing Grok API configuration. Please check GROK_API_URL and GROK_API_KEY environment variables.');
      }

      // Log API request details (without sensitive info)
      console.log(`Making API request to ${config.grokApiUrl} with model ${config.grokModel}`);

      const apiPromise = axios.post(config.grokApiUrl, {
        messages: [
          {
            role: 'system',
            content: "You are an AI assistant that provides helpful product recommendations related to given keywords."
          },
          {
            role: 'user',
            content: apiPrompt
          }
        ],
        model: config.grokModel, // Using the model specified in environment variables
        stream: false,
        temperature: 0.4 // Moderate temperature for creative yet relevant recommendations
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.grokApiKey}`
        },
        timeout: config.timeout
      });

      const response = await Promise.race([apiPromise, timeoutPromise]);

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const content = response.data.choices[0].message.content.trim();
        console.log(`Raw affiliate resources response (attempt ${attempt + 1}):`, content);

        try {
          // Find the JSON part in case the model adds extra text
          const jsonMatch = content.match(/\[\s\S]*\]/);
          if (!jsonMatch) {
            throw new Error("No JSON array found in the response.");
          }
          const jsonString = jsonMatch[0];
          const resources = JSON.parse(jsonString);

          // Validate the structure and URLs
          if (Array.isArray(resources)) {
            const validatedResources = resources.filter(resource =>
              resource &&
              typeof resource.title === 'string' &&
              typeof resource.description === 'string' &&
              typeof resource.price === 'string' &&
              typeof resource.url === 'string' && isValidHttpUrl(resource.url) &&
              typeof resource.imageUrl === 'string' && isValidHttpUrl(resource.imageUrl)
            ).slice(0, 3); // Limit to 3 valid resources

            console.log(`Affiliate resources generation successful (attempt ${attempt + 1}), Validated Resources: ${validatedResources.length}`);
            return validatedResources;
          } else {
            throw new Error('Parsed response JSON is not an array.');
          }
        } catch (parseError) {
          console.error(`Failed to parse affiliate resources JSON (attempt ${attempt + 1}):`, parseError, 'Raw content:', content);
          throw new Error(`Invalid JSON response for affiliate resources: ${content}`);
        }
      } else {
        throw new Error('Invalid response structure from Grok API for affiliate resources');
      }
    } catch (error) {
      lastError = error;
      console.error(`Affiliate resources generation attempt ${attempt + 1} failed:`, error.message);

      if (attempt < config.affiliateMaxRetries) {
        console.log(`Waiting ${config.retryDelay}ms before retry...`);
        await delay(config.retryDelay);
      }
    }
  }

  console.error('All affiliate resources generation attempts failed. Last error:', lastError?.message);
  // Return empty array on failure
  return [];
}

// Find tips and links using Grok API
async function findTipsAndLinks(keywords, _userContext) {
  // TODO: Potentially use _userContext to personalize tips/links
  console.log('Finding tips/links for keywords:', keywords);

  if (!keywords || keywords.length === 0) {
    console.log('Skipping tips/links generation due to no keywords.');
    return { tips: [], links: [] };
  }

  let lastError;
  const keywordsString = keywords.join(', '); // Format keywords for the prompt

  for (let attempt = 0; attempt <= config.tipsLinksMaxRetries; attempt++) {
    try {
      console.log(`Tips/links generation attempt ${attempt + 1}`);

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Grok API timeout during tips/links generation')), config.timeout * 0.8);
      });

      // Enhanced prompt for more specific, "real" content
      const apiPrompt = `Given the keywords: "${keywordsString}", generate highly specific and useful tips and links that would be valuable to someone researching this topic.

For TIPS: Provide exactly 2 practical, actionable tips that offer genuine value. Each tip should include a specific recommendation, not generic advice. Include URLs to real, authoritative resources when possible.

For LINKS: Provide exactly 2 links to high-quality, specific external resources (like articles, tools, or guides) directly related to the keywords. Choose resources from reputable sites that would genuinely help someone learn about this topic. Avoid generic search results.

Respond **only** with a valid JSON object following this exact structure:
{
  "tips": [
    {"title": "specific, actionable title", "description": "detailed, helpful description", "url": "valid_url_to_real_resource_or_null"}
  ],
  "links": [
    {"title": "specific resource title", "url": "valid_url_to_real_resource"}
  ]
}

Ensure all provided URLs start with http:// or https:// and point to real, existing resources. If no suitable URL exists for a tip, set its url value to null.`;

      // Wait if needed to avoid rate limiting
      await apiCallTracker.waitIfNeeded();

      // Validate API configuration before making the call
      if (!config.grokApiUrl || !config.grokApiKey) {
        throw new Error('Missing Grok API configuration. Please check GROK_API_URL and GROK_API_KEY environment variables.');
      }

      // Log API request details (without sensitive info)
      console.log(`Making API request to ${config.grokApiUrl} with model ${config.grokModel}`);

      const apiPromise = axios.post(config.grokApiUrl, {
        messages: [
          {
            role: 'system',
            content: "You are an AI assistant that provides helpful tips and links related to given keywords."
          },
          {
            role: 'user',
            content: apiPrompt
          }
        ],
        model: config.grokModel, // Using the model specified in environment variables
        stream: false,
        temperature: 0.4 // Moderate temperature for creative yet fast responses
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.grokApiKey}`
        },
        timeout: config.timeout
      });

      const response = await Promise.race([apiPromise, timeoutPromise]);

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const content = response.data.choices[0].message.content.trim();
        console.log(`Raw tips/links response (attempt ${attempt + 1}):`, content);
        // Try to parse the JSON object directly
        try {
          // Find the JSON part in case the model adds extra text
          const jsonMatch = content.match(/\{[\s\S]*\}/);
          if (!jsonMatch) {
              throw new Error("No JSON object found in the response.");
          }
          const jsonString = jsonMatch[0];
          const result = JSON.parse(jsonString);

          // Validate the structure and URLs
          if (result && Array.isArray(result.tips) && Array.isArray(result.links)) {
            const validatedTips = result.tips.filter(tip =>
                tip && typeof tip.title === 'string' && typeof tip.description === 'string' && (tip.url === null || (typeof tip.url === 'string' && isValidHttpUrl(tip.url)))
            ).slice(0, 2); // Limit to 2 valid tips

            const validatedLinks = result.links.filter(link =>
                link && typeof link.title === 'string' && typeof link.url === 'string' && isValidHttpUrl(link.url)
            ).slice(0, 2); // Limit to 2 valid links

            console.log(`Tips/links generation successful (attempt ${attempt + 1}), Validated Tips: ${validatedTips.length}, Validated Links: ${validatedLinks.length}`);
            return {
                tips: validatedTips,
                links: validatedLinks
            };
          } else {
            throw new Error('Parsed response JSON does not have the expected tips/links array structure.');
          }
        } catch (parseError) {
          console.error(`Failed to parse tips/links JSON (attempt ${attempt + 1}):`, parseError, 'Raw content:', content);
          throw new Error(`Invalid JSON response for tips/links: ${content}`);
        }
      } else {
        throw new Error('Invalid response structure from Grok API for tips/links');
      }
    } catch (error) {
      lastError = error;
      console.error(`Tips/links generation attempt ${attempt + 1} failed:`, error.message);

      if (attempt < config.tipsLinksMaxRetries) {
        console.log(`Waiting ${config.retryDelay}ms before retry...`);
        await delay(config.retryDelay);
      }
    }
  }

  console.error('All tips/links generation attempts failed. Last error:', lastError?.message);
  // Return empty arrays on failure
  return { tips: [], links: [] };
}

// The main TipTop endpoint handler
async function handleTipTopRequest(req, res) {
  const startTime = Date.now();
  try {
    const { url, userContext } = req.body;
    console.log(`Received /tiptop request for URL: ${url}`);

    if (!url) {
      return res.status(400).json({ error: 'URL is required.' });
    }

    // Check cache first
    const cachedResponse = getCachedResponse(url);
    if (cachedResponse) {
      // Add cache info to response
      cachedResponse.fromCache = true;
      cachedResponse.originalResponseTime = cachedResponse.responseTime;
      cachedResponse.responseTime = Date.now() - startTime;

      // Ensure all expected fields are present in cached response
      // Removed reading time calculation from cached responses

      // For cached responses, if there are no key takeaways, leave it as an empty array
      // This will hide the key takeaways section when there are no specific insights
      if (!cachedResponse.keyTakeaways) {
        cachedResponse.keyTakeaways = [];
      }

      // Handle the transition from tips/links to combined resources (displayed as "Tips & External Links")
      if (!cachedResponse.resources) {
        // If we have old format with tips and links, combine them
        if (cachedResponse.tips || cachedResponse.links) {
          const combinedResources = [...(cachedResponse.tips || [])];

          // Add links to combined resources if they exist
          if (cachedResponse.links && cachedResponse.links.length > 0) {
            cachedResponse.links.forEach(link => {
              combinedResources.push({
                title: `Related: ${link.title}`,
                description: `External resource related to this topic.`,
                url: link.url
              });
            });
          }

          cachedResponse.resources = combinedResources;
        } else {
          // Ensure we have at least a default resource to display the section
          cachedResponse.resources = [{
            title: "Explore Related Content",
            description: "Search for more information about this topic using your preferred search engine.",
            url: null
          }];
        }
      }

      // Ensure resources has at least 2 items with useful content and URLs
      if (cachedResponse.resources.length < 2) {
        // Generate more specific default resources for cached responses
        // Extract potential keywords from the URL or title
        let potentialKeyword = "";
        if (cachedResponse.title) {
          // Extract the first few words from the title as a potential keyword
          const titleWords = cachedResponse.title.split(/\s+/);
          potentialKeyword = titleWords.slice(0, 2).join(" ");
        } else if (url) {
          // Try to extract a keyword from the URL
          const urlParts = url.split("/").pop().split("?")[0].split("-");
          if (urlParts.length > 1) {
            potentialKeyword = urlParts.slice(0, 2).join(" ");
          }
        }

        const defaultResources = [
          {
            title: `${potentialKeyword ? potentialKeyword + " Guide" : "Topic Guide"} from Harvard University`,
            description: `Harvard University offers comprehensive resources on ${potentialKeyword || "various topics"} with expert insights and practical applications.`,
            url: `https://www.harvard.edu/search/?q=${encodeURIComponent(potentialKeyword || "guide")}`
          },
          {
            title: `${potentialKeyword ? potentialKeyword + " Research" : "Research"} on MIT OpenCourseWare`,
            description: `MIT OpenCourseWare provides free access to course materials on ${potentialKeyword || "various subjects"} from one of the world's leading universities.`,
            url: `https://ocw.mit.edu/search/?q=${encodeURIComponent(potentialKeyword || "")}`
          },
          {
            title: `${potentialKeyword ? potentialKeyword + " Articles" : "Articles"} on Nature.com`,
            description: `Nature.com offers peer-reviewed research and articles on ${potentialKeyword || "various scientific topics"} from one of the most respected scientific journals.`,
            url: `https://www.nature.com/search?q=${encodeURIComponent(potentialKeyword || "")}`
          }
        ];

        // Add default resources until we have at least 2
        for (let i = 0; i < defaultResources.length && cachedResponse.resources.length < 2; i++) {
          cachedResponse.resources.push(defaultResources[i]);
        }
      }

      // Ensure all resources have a URL (even if it's just a search URL)
      cachedResponse.resources = cachedResponse.resources.map(resource => {
        if (!resource.url) {
          return {
            ...resource,
            url: "https://www.google.com/search?q=" + encodeURIComponent(resource.title)
          };
        }
        return resource;
      });

      if (!cachedResponse.affiliateResources) {
        cachedResponse.affiliateResources = [];
      }

      // Ensure we have at least 2 default affiliate resources if none or too few were retrieved
      if (cachedResponse.affiliateResources.length < 2) {
        // Generate more specific default affiliate resources for cached responses
        // Use the same potential keyword extraction logic as for resources
        let potentialKeyword = "";
        if (cachedResponse.title) {
          // Extract the first few words from the title as a potential keyword
          const titleWords = cachedResponse.title.split(/\s+/);
          potentialKeyword = titleWords.slice(0, 2).join(" ");
        } else if (url) {
          // Try to extract a keyword from the URL
          const urlParts = url.split("/").pop().split("?")[0].split("-");
          if (urlParts.length > 1) {
            potentialKeyword = urlParts.slice(0, 2).join(" ");
          }
        }

        const defaultAffiliateResources = [
          {
            title: `"${potentialKeyword || "Essential Knowledge"}" by Oxford University Press`,
            description: `This authoritative book from Oxford University Press provides comprehensive coverage of ${potentialKeyword || "important topics"} with expert insights and analysis.`,
            //price: "$24.95",
            url: `https://global.oup.com/academic/search?q=${encodeURIComponent(potentialKeyword || "")}&cc=us&lang=en`,
            imageUrl: getResourceIconDataUrl('book')
          },
          {
            title: `${potentialKeyword || "Expert"} Masterclass on Coursera`,
            description: `This specialized Coursera course offers in-depth training on ${potentialKeyword || "various subjects"} taught by industry experts and leading academics.`,
            //price: "$49.99",
            url: `https://www.coursera.org/search?query=${encodeURIComponent(potentialKeyword || "")}`,
            imageUrl: getResourceIconDataUrl('course')
          },
          {
            title: `Professional ${potentialKeyword || "Learning"} Toolkit by ThinkGeek`,
            description: `This comprehensive toolkit provides all the resources you need to master ${potentialKeyword || "new skills"} with practical tools and guides.`,
            //price: "$79.95",
            url: `https://www.amazon.com/s?k=${encodeURIComponent((potentialKeyword || "") + " professional toolkit")}`,
            imageUrl: getResourceIconDataUrl('tool')
          }
        ];

        // Add default resources until we have at least 2
        for (let i = 0; i < defaultAffiliateResources.length && cachedResponse.affiliateResources.length < 2; i++) {
          cachedResponse.affiliateResources.push(defaultAffiliateResources[i]);
        }
      }

      // Ensure all affiliate resources have valid image URLs using SVG data URLs
      cachedResponse.affiliateResources = cachedResponse.affiliateResources.map(resource => {
        if (!resource.imageUrl || !isValidHttpUrl(resource.imageUrl)) {
          // Determine resource type based on title
          let resourceType = 'default';
          const title = resource.title.toLowerCase();

          if (title.includes('book') || title.includes('read') || title.includes('publication')) {
            resourceType = 'book';
          } else if (title.includes('course') || title.includes('class') || title.includes('learn') || title.includes('training')) {
            resourceType = 'course';
          } else if (title.includes('tool') || title.includes('software') || title.includes('app')) {
            resourceType = 'tool';
          } else if (title.includes('video') || title.includes('watch') || title.includes('tutorial')) {
            resourceType = 'video';
          }

          return {
            ...resource,
            imageUrl: getResourceIconDataUrl(resourceType)
          };
        }
        return resource;
      });

      // Remove keywords as they're no longer needed
      delete cachedResponse.keywords;
      delete cachedResponse.tips;
      delete cachedResponse.links;

      console.log(`Returning cached response for URL: ${url} (original processing time: ${cachedResponse.originalResponseTime}ms, current: ${cachedResponse.responseTime}ms)`);
      return res.json(cachedResponse);
    }

    // Special handling for known problematic domains
    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.toLowerCase();

      // Check if this is a Wikipedia article or other known large content site
      const isWikipedia = domain.includes('wikipedia.org') && url.includes('/wiki/');
      const isLargeContentSite = [
        'wikipedia.org',
        'docs.google.com',
        'github.com',
        'arxiv.org',
        'academia.edu'
      ].some(site => domain.includes(site));

      if (isWikipedia) {
        console.log(`Detected Wikipedia article: ${url}`);
        try {
          // Use our specialized Wikipedia handler
          return await handleWikipediaArticle(url, res, startTime);
        } catch (wikiError) {
          console.error('Error in Wikipedia special handling:', wikiError);
          // Fall back to standard processing if special handling fails
        }
      } else if (isLargeContentSite) {
        console.log(`Detected potentially large content site: ${domain}`);
        // We'll continue with standard processing but with extra caution
      }
    } catch (urlError) {
      console.warn('Error parsing URL for domain detection:', urlError);
      // Continue with standard processing
    }

    // 1. Fetch and Parse
    const article = await fetchAndParse(url);
    if (!article || !article.textContent) {
      return res.status(400).json({ error: 'Could not extract readable content from the URL.' });
    }

    // Removed reading time calculation to reduce load

    // Prepare text for processing with special handling for extremely long content
    const STANDARD_MAX_LENGTH = 8000; // Standard max length for normal articles
    const EXTREMELY_LONG_THRESHOLD = 50000; // Threshold to consider content extremely long
    const INTRO_LENGTH = 3000; // Length to extract for introduction in extremely long content

    let processedText;
    let isExtremelyLong = false;

    if (article.textContent.length > EXTREMELY_LONG_THRESHOLD) {
      // Special handling for extremely long content (like Wikipedia articles)
      isExtremelyLong = true;
      console.log(`Content is extremely long (${article.textContent.length} chars), using special handling mode`);

      // Extract just the introduction (first part of the content)
      const introduction = article.textContent.substring(0, INTRO_LENGTH);

      // Try to extract section headings from the HTML content to understand structure
      const headings = [];
      try {
        // Create a new DOM from the HTML content
        const contentDom = new JSDOM(article.content);
        const headingElements = contentDom.window.document.querySelectorAll('h1, h2, h3');

        // Extract up to 10 headings
        for (let i = 0; i < Math.min(headingElements.length, 10); i++) {
          const heading = headingElements[i].textContent.trim();
          if (heading) headings.push(heading);
        }

        console.log(`Extracted ${headings.length} section headings from extremely long content`);
      } catch (headingError) {
        console.warn('Failed to extract headings from HTML content:', headingError.message);
      }

      // Combine introduction with headings
      processedText = introduction + "\n\nThis content is extremely long and contains the following sections:\n" +
                     headings.map(h => `- ${h}`).join('\n');

      console.log(`Processed extremely long text (${article.textContent.length} chars) to ${processedText.length} chars`);
    } else if (article.textContent.length > STANDARD_MAX_LENGTH) {
      // Standard truncation for moderately long content
      // Take the first 4000 characters (usually contains the most important info)
      const beginning = article.textContent.substring(0, 4000);
      // Take the last 4000 characters
      const end = article.textContent.substring(article.textContent.length - 4000);
      // Combine with markers
      processedText = beginning + "\n[...content omitted...]\n" + end;
      console.log(`Text truncated from ${article.textContent.length} to approximately ${processedText.length} characters`);
    } else {
      processedText = article.textContent;
    }

    // OPTIMIZATION: Reduce API calls by combining related tasks
    // We'll make just 2 API calls instead of 5:
    // 1. Get summary and keywords in parallel (most important)
    // 2. Get tips/links and takeaways in parallel (based on keywords from first call)

    console.log('Starting first batch of API calls (summary and keywords)');
    // First batch of API calls (in parallel)
    const [summaryText, keywords] = await Promise.all([
      summarizeText(processedText),
      extractKeywords(processedText)
    ]);

    console.log('First batch completed, starting second batch');
    // Second batch of API calls (in parallel, using keywords from first batch)
    // We'll skip affiliate resources if we're running short on time
    const secondBatchStartTime = Date.now();
    const timeElapsed = secondBatchStartTime - startTime;

    // If we're already over 12 seconds, skip the second batch to avoid timeout
    let keyTakeaways = [];
    let tips = [];
    let links = [];
    let affiliateResources = [];

    // Always include at least the tips/links call, even if we're running short on time
    // This ensures the Tips & Links section always displays
    if (timeElapsed < 12000) { // Increased time threshold to ensure Tips & Links are processed
      try {
        // Second batch with reduced timeout
        const reducedTimeout = Math.max(5000, config.timeout - timeElapsed);
        console.log(`Using reduced timeout for second batch: ${reducedTimeout}ms`);

        // Choose which API calls to make based on remaining time
        let secondBatchPromises = [];
        let takeawaysPromise, tipsLinksPromise;

        // Always prioritize tips/links and affiliate resources over other features
        if (keywords.length > 0) {
          // First, ensure we get tips/links and affiliate resources (most important for UI)
          // These are the two sections that need "real" content
          tipsLinksPromise = findTipsAndLinks(keywords, userContext);
          const affiliatePromise = generateAffiliateResources(keywords);

          // Run these two critical requests in parallel
          secondBatchPromises = [tipsLinksPromise, affiliatePromise];

          // Add takeaways only if we have extra time (less important than the UI sections)
          if (timeElapsed < 9000) {
            takeawaysPromise = extractKeyTakeaways(processedText, keywords);
            secondBatchPromises.push(takeawaysPromise);
          }

          // Execute second batch with custom timeout
          const secondBatchResults = await Promise.allSettled(secondBatchPromises);

          // Process results (handle both fulfilled and rejected promises)
          // First result is always tips/links (most important for UI)
          if (secondBatchResults[0].status === 'fulfilled') {
            const tipsAndLinks = secondBatchResults[0].value;
            tips = tipsAndLinks.tips || [];
            links = tipsAndLinks.links || [];
            console.log(`Successfully retrieved tips (${tips.length}) and links (${links.length})`);
          } else {
            console.error('Failed to retrieve tips and links:', secondBatchResults[0].reason);
          }

          // Second result is takeaways (if we had time to request them)
          if (secondBatchPromises.length > 1 && secondBatchResults[1].status === 'fulfilled') {
            keyTakeaways = secondBatchResults[1].value;
            console.log(`Successfully retrieved key takeaways (${keyTakeaways.length})`);
          } else {
            // If we couldn't get specific takeaways from the API, don't show generic ones
            // This ensures the key takeaways section only displays when we have valuable content
            keyTakeaways = [];
            console.log('No specific key takeaways found - section will be hidden');
            console.log(`Generated default key takeaways (${keyTakeaways.length})`);
          }

          // Third result is affiliate resources (if we had time to request them)
          if (secondBatchPromises.length > 2 && secondBatchResults[2].status === 'fulfilled') {
            affiliateResources = secondBatchResults[2].value;
            console.log(`Successfully retrieved affiliate resources (${affiliateResources.length})`);
          }
        }
      } catch (error) {
        console.error('Error in second batch of API calls:', error);
        // Continue with what we have
      }
    } else {
      console.log('Skipping second batch of API calls due to time constraints');
    }

    // Combine tips and links into a single "resources" section (displayed as "Tips & External Links")
    const combinedResources = [...tips];

    // Add links to the combined resources with a different format
    if (links && links.length > 0) {
      links.forEach(link => {
        combinedResources.push({
          title: `Related: ${link.title}`,
          description: `External resource related to ${keywords[0] || 'this topic'}.`,
          url: link.url
        });
      });
    }

    // Ensure we have at least 2 useful default resources if none or too few were retrieved
    // This helps ensure the Tips & Links section always displays with useful content
    if (combinedResources.length < 2) {
      // Generate more specific default resources based on the keywords
      const keywordForSearch = keywords && keywords.length > 0 ? keywords[0] : "";
      const defaultResources = [
        {
          title: `${keywordForSearch ? keywordForSearch + " Guide" : "Topic Guide"} from Harvard University`,
          description: `Harvard University offers comprehensive resources on ${keywordForSearch || "various topics"} with expert insights and practical applications.`,
          url: `https://www.harvard.edu/search/?q=${encodeURIComponent(keywordForSearch || "guide")}`
        },
        {
          title: `${keywordForSearch ? keywordForSearch + " Research" : "Research"} on MIT OpenCourseWare`,
          description: `MIT OpenCourseWare provides free access to course materials on ${keywordForSearch || "various subjects"} from one of the world's leading universities.`,
          url: `https://ocw.mit.edu/search/?q=${encodeURIComponent(keywordForSearch || "")}`
        },
        {
          title: `${keywordForSearch ? keywordForSearch + " Articles" : "Articles"} on Nature.com`,
          description: `Nature.com offers peer-reviewed research and articles on ${keywordForSearch || "various scientific topics"} from one of the most respected scientific journals.`,
          url: `https://www.nature.com/search?q=${encodeURIComponent(keywordForSearch || "")}`
        }
      ];

      // Add default resources until we have at least 2
      for (let i = 0; i < defaultResources.length && combinedResources.length < 2; i++) {
        combinedResources.push(defaultResources[i]);
      }
    }

    // We'll determine if this is a partial response later

    // Ensure we have at least 2 default affiliate resources if none or too few were retrieved
    // This helps ensure the Related Resources section always displays with useful content
    if (affiliateResources.length < 2) {
      // Generate more specific default affiliate resources based on the keywords
      const keywordForSearch = keywords && keywords.length > 0 ? keywords[0] : "";
      const defaultAffiliateResources = [
        {
          title: `"${keywordForSearch || "Essential Knowledge"}" by Oxford University Press`,
          description: `This authoritative book from Oxford University Press provides comprehensive coverage of ${keywordForSearch || "important topics"} with expert insights and analysis.`,
          //price: "$24.95",
          url: `https://global.oup.com/academic/search?q=${encodeURIComponent(keywordForSearch || "")}&cc=us&lang=en`,
          imageUrl: getResourceIconDataUrl('book')
        },
        {
          title: `${keywordForSearch || "Expert"} Masterclass on Coursera`,
          description: `This specialized Coursera course offers in-depth training on ${keywordForSearch || "various subjects"} taught by industry experts and leading academics.`,
          //price: "$49.99",
          url: `https://www.coursera.org/search?query=${encodeURIComponent(keywordForSearch || "")}`,
          imageUrl: getResourceIconDataUrl('course')
        },
        {
          title: `Professional ${keywordForSearch || "Learning"} Toolkit by ThinkGeek`,
          description: `This comprehensive toolkit provides all the resources you need to master ${keywordForSearch || "new skills"} with practical tools and guides.`,
          //price: "$79.95",
          url: `https://www.amazon.com/s?k=${encodeURIComponent((keywordForSearch || "") + " professional toolkit")}`,
          imageUrl: getResourceIconDataUrl('tool')
        }
      ];

      // Add default resources until we have at least 2
      for (let i = 0; i < defaultAffiliateResources.length && affiliateResources.length < 2; i++) {
        affiliateResources.push(defaultAffiliateResources[i]);
      }
    }

    // Ensure all affiliate resources have valid image URLs using SVG data URLs
    affiliateResources = affiliateResources.map(resource => {
      if (!resource.imageUrl || !isValidHttpUrl(resource.imageUrl)) {
        // Determine resource type based on title
        let resourceType = 'default';
        const title = resource.title.toLowerCase();

        if (title.includes('book') || title.includes('read') || title.includes('publication')) {
          resourceType = 'book';
        } else if (title.includes('course') || title.includes('class') || title.includes('learn') || title.includes('training')) {
          resourceType = 'course';
        } else if (title.includes('tool') || title.includes('software') || title.includes('app')) {
          resourceType = 'tool';
        } else if (title.includes('video') || title.includes('watch') || title.includes('tutorial')) {
          resourceType = 'video';
        }

        return {
          ...resource,
          imageUrl: getResourceIconDataUrl(resourceType)
        };
      }
      return resource;
    });

    // Construct Response
    const responseTime = Date.now() - startTime;

    // Determine if we should send a partial response for asynchronous loading
    const isPartialResponse = timeElapsed > 8000;

    // Create the base response with the summary (always included)
    // Add appropriate message based on content length and truncation
    let summaryMessage = summaryText;

    if (article.wasTruncated) {
      // HTML was truncated before processing
      summaryMessage += "\n\nNote: This page is extremely large. Only a portion of the content could be processed.";
    } else if (isExtremelyLong) {
      // Content was extremely long but HTML wasn't truncated
      summaryMessage += "\n\nNote: This content is extremely long. The summary is based on the introduction and main sections only.";
    }

    const responsePayload = {
      summary: {
        text: summaryMessage,
        source: "Grok AI" // Updated source
      },
      keyTakeaways: keyTakeaways,
      responseTime: responseTime,
      fromCache: false,
      // Add flags for asynchronous loading
      isPartialResponse: isPartialResponse,
      pendingResources: isPartialResponse,
      pendingAffiliateResources: isPartialResponse,
      requestId: Date.now().toString(), // Unique ID for this request to use with follow-up requests
      isExtremelyLong: isExtremelyLong, // Flag to indicate extremely long content
      wasTruncated: article.wasTruncated || false // Flag to indicate if HTML was truncated
    };

    // If key takeaways are empty, the section will be hidden in the UI
    // This is intentional - we only want to show specific, valuable insights

    // Only include resources and affiliateResources if this is not a partial response
    // or if they've already been processed despite the time constraint
    if (!isPartialResponse || combinedResources.length > 0) {
      responsePayload.resources = combinedResources;
      responsePayload.pendingResources = false;
    }

    if (!isPartialResponse || affiliateResources.length > 0) {
      responsePayload.affiliateResources = affiliateResources;
      responsePayload.pendingAffiliateResources = false;
    }

    // Cache the response for future requests
    cacheResponse(url, responsePayload);

    console.log(`Sending /tiptop response for URL: ${url} (processing time: ${responseTime}ms)`);
    res.json(responsePayload);

  } catch (error) {
    // Enhanced error logging with stack trace and more context
    console.error('Error in /tiptop endpoint:', error);
    console.error('Error stack:', error.stack);
    console.error('Request URL:', url);
    console.error('Processing time before error:', Date.now() - startTime, 'ms');

    // Determine if this is a timeout error
    const isTimeoutError = error.message && (
      error.message.includes('timeout') ||
      error.message.includes('ETIMEDOUT') ||
      error.message.includes('ESOCKETTIMEDOUT') ||
      error.code === 'ECONNABORTED'
    );

    // Check if this is an error related to extremely long content
    const isLongContentError = error.message && (
      error.message.includes('extremely large') ||
      error.message.includes('content is too long') ||
      error.message.includes('content is very long')
    );

    // Provide a more specific error message based on the error type
    let errorMessage = 'Internal server error processing TipTop request';
    let statusCode = 500;

    if (isLongContentError) {
      errorMessage = 'This page is too large to process completely. Try a smaller page or a specific section of this content.';
      statusCode = 413; // Payload Too Large
    } else if (isTimeoutError) {
      errorMessage = 'The request timed out. The page may be too large or complex to process quickly.';
      statusCode = 504; // Gateway Timeout
    } else if (error.message && error.message.includes('ECONNREFUSED')) {
      errorMessage = 'Could not connect to the AI service. Please try again later.';
      statusCode = 503; // Service Unavailable
    } else if (error.response && error.response.status) {
      // Handle API-specific errors
      statusCode = error.response.status;
      errorMessage = `AI service returned an error (${statusCode})`;
      if (error.response.data && error.response.data.error) {
        errorMessage += `: ${error.response.data.error}`;
      }
    }

    // Return a more informative error response
    res.status(statusCode).json({
      error: errorMessage,
      details: error.message,
      requestId: Date.now().toString(), // Add a request ID for tracking
      // Include a simplified summary if possible
      summary: {
        text: "Sorry, we couldn't process this page. Please try a different page or try again later.",
        source: "Error Handler"
      }
    });
  }
}

// Register the TipTop endpoint
app.post('/tiptop', handleTipTopRequest);

// Endpoint for handling AI questions - accessible at both /ask and /tiptop/ask
const handleAskRequest = async (req, res) => {
  try {
    const { url, question } = req.body;
    console.log(`Received /ask request for URL: ${url}, question: ${question}`);

    if (!url || !question) {
      return res.status(400).json({ error: 'URL and question are required.' });
    }

    // Fetch the content of the URL to provide context for the AI
    let article;
    try {
      article = await fetchAndParse(url);
      console.log(`Successfully fetched and parsed content for AI question: ${url}`);
    } catch (fetchError) {
      console.error(`Error fetching content for AI question: ${fetchError.message}`);
      return res.status(500).json({
        error: `Failed to fetch content for analysis: ${fetchError.message}`,
        answer: "I'm sorry, I couldn't analyze the page content to answer your question. Please try again later."
      });
    }

    // Create a timeout promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Grok API timeout during AI question answering')), config.timeout);
    });

    // Wait if needed to avoid rate limiting
    await apiCallTracker.waitIfNeeded();

    // Prepare the context and question for the AI
    const context = article.textContent.substring(0, 10000); // Limit context to 10000 characters

    // Log API request details (without sensitive info)
    console.log(`Making AI question API request to ${config.grokApiUrl} with model ${config.grokModel}`);

    // Make the API call
    try {
      const apiPromise = axios.post(config.grokApiUrl, {
        messages: [
          {
            role: 'system',
            content: "You are an AI assistant that answers questions about web page content. Use the provided context to answer the user's question. If the answer cannot be determined from the context, say so clearly but provide your best guess based on general knowledge. Be concise but thorough."
          },
          {
            role: 'user',
            content: `Context from web page: "${context}"\n\nQuestion: ${question}`
          }
        ],
        model: config.grokModel,
        stream: false,
        temperature: 0.3
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.grokApiKey}`
        },
        timeout: config.timeout
      });

      // Race between timeout and API call
      const response = await Promise.race([apiPromise, timeoutPromise]);

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const answer = response.data.choices[0].message.content.trim();
        console.log(`AI question answering successful for: ${question}`);
        return res.json({ answer });
      } else {
        throw new Error('Invalid response structure from Grok API');
      }
    } catch (error) {
      console.error(`AI question answering failed: ${error.message}`);
      return res.status(500).json({
        error: `Failed to generate answer: ${error.message}`,
        answer: "I'm sorry, I couldn't generate an answer to your question at this time. Please try again later."
      });
    }
  } catch (error) {
    console.error('Error in /ask endpoint:', error);
    return res.status(500).json({
      error: 'Internal server error processing AI question',
      answer: "I'm sorry, an error occurred while processing your question. Please try again later."
    });
  }
};

// Register routes to handle the ask endpoint with all possible paths
app.post('/ask', handleAskRequest);
app.post('/tiptop/ask', handleAskRequest);
// Add a route for the case where the client might be using a different base path
app.use('/tiptop', (req, res, next) => {
  // Log the request for debugging
  console.log(`Received request at /tiptop${req.path} with method ${req.method}`);
  next();
});

// Log the registered routes
console.log('Registered AI question endpoints: /ask and /tiptop/ask');

// Endpoint for asynchronously fetching remaining resources
app.post('/tiptop-resources', async (req, res) => {
  try {
    const { url, requestId, resourceType } = req.body;
    console.log(`Received /tiptop-resources request for URL: ${url}, requestId: ${requestId}, resourceType: ${resourceType}`);

    if (!url || !requestId || !resourceType) {
      return res.status(400).json({ error: 'URL, requestId, and resourceType are required.' });
    }

    // Check cache first
    const cachedResponse = getCachedResponse(url);
    if (cachedResponse) {
      // Return the requested resource type from cache
      if (resourceType === 'tips-links' && cachedResponse.resources) {
        return res.json({ resources: cachedResponse.resources, requestId });
      } else if (resourceType === 'affiliate' && cachedResponse.affiliateResources) {
        return res.json({ affiliateResources: cachedResponse.affiliateResources, requestId });
      } else if (resourceType === 'key-takeaways' && cachedResponse.keyTakeaways) {
        return res.json({ keyTakeaways: cachedResponse.keyTakeaways, requestId });
      }
    }

    // If not in cache, we need to generate the resources
    // First, fetch and parse the content
    const article = await fetchAndParse(url);
    if (!article || !article.textContent) {
      return res.status(400).json({ error: 'Could not extract readable content from the URL.' });
    }

    // Extract keywords (needed for both resource types)
    const keywords = await extractKeywords(article.textContent);

    if (resourceType === 'key-takeaways') {
      // Generate key takeaways
      const processedText = article.textContent.substring(0, 8000); // Limit text length
      let keyTakeaways = await extractKeyTakeaways(processedText, keywords);

      // If no specific key takeaways were found, return an empty array
      // This will hide the key takeaways section in the UI
      if (!keyTakeaways) {
        keyTakeaways = [];
      }

      // Update cache with the new key takeaways
      if (cachedResponse) {
        cachedResponse.keyTakeaways = keyTakeaways;
        cacheResponse(url, cachedResponse);
      }

      return res.json({ keyTakeaways, requestId });

    } else if (resourceType === 'tips-links') {
      // Generate tips and links
      const tipsAndLinks = await findTipsAndLinks(keywords);
      const tips = tipsAndLinks.tips || [];
      const links = tipsAndLinks.links || [];

      // Combine tips and links
      const combinedResources = [...tips];
      if (links && links.length > 0) {
        links.forEach(link => {
          combinedResources.push({
            title: `Related: ${link.title}`,
            description: `External resource related to ${keywords[0] || 'this topic'}.`,
            url: link.url
          });
        });
      }

      // Ensure we have at least 2 resources
      if (combinedResources.length < 2) {
        const keywordForSearch = keywords && keywords.length > 0 ? keywords[0] : "";
        const defaultResources = [
          {
            title: `${keywordForSearch ? keywordForSearch + " Guide" : "Topic Guide"} from Harvard University`,
            description: `Harvard University offers comprehensive resources on ${keywordForSearch || "various topics"} with expert insights and practical applications.`,
            url: `https://www.harvard.edu/search/?q=${encodeURIComponent(keywordForSearch || "guide")}`
          },
          {
            title: `${keywordForSearch ? keywordForSearch + " Research" : "Research"} on MIT OpenCourseWare`,
            description: `MIT OpenCourseWare provides free access to course materials on ${keywordForSearch || "various subjects"} from one of the world's leading universities.`,
            url: `https://ocw.mit.edu/search/?q=${encodeURIComponent(keywordForSearch || "")}`
          }
        ];

        for (let i = 0; i < defaultResources.length && combinedResources.length < 2; i++) {
          combinedResources.push(defaultResources[i]);
        }
      }

      // Update cache with the new resources
      if (cachedResponse) {
        cachedResponse.resources = combinedResources;
        cacheResponse(url, cachedResponse);
      }

      return res.json({ resources: combinedResources, requestId });

    } else if (resourceType === 'affiliate') {
      // Generate affiliate resources
      let affiliateResources = await generateAffiliateResources(keywords);

      // Ensure we have at least 2 resources
      if (affiliateResources.length < 2) {
        const keywordForSearch = keywords && keywords.length > 0 ? keywords[0] : "";
        const defaultAffiliateResources = [
          {
            title: `"${keywordForSearch || "Essential Knowledge"}" by Oxford University Press`,
            description: `This authoritative book from Oxford University Press provides comprehensive coverage of ${keywordForSearch || "important topics"} with expert insights and analysis.`,
            //price: "$24.95",
            url: `https://global.oup.com/academic/search?q=${encodeURIComponent(keywordForSearch || "")}&cc=us&lang=en`,
            imageUrl: getResourceIconDataUrl('book')
          },
          {
            title: `${keywordForSearch || "Expert"} Masterclass on Coursera`,
            description: `This specialized Coursera course offers in-depth training on ${keywordForSearch || "various subjects"} taught by industry experts and leading academics.`,
            //price: "$49.99",
            url: `https://www.coursera.org/search?query=${encodeURIComponent(keywordForSearch || "")}`,
            imageUrl: getResourceIconDataUrl('course')
          }
        ];

        for (let i = 0; i < defaultAffiliateResources.length && affiliateResources.length < 2; i++) {
          affiliateResources.push(defaultAffiliateResources[i]);
        }
      }

      // Ensure all affiliate resources have valid image URLs
      affiliateResources = affiliateResources.map(resource => {
        if (!resource.imageUrl || !isValidHttpUrl(resource.imageUrl)) {
          let resourceType = 'default';
          const title = resource.title.toLowerCase();

          if (title.includes('book') || title.includes('read') || title.includes('publication')) {
            resourceType = 'book';
          } else if (title.includes('course') || title.includes('class') || title.includes('learn')) {
            resourceType = 'course';
          } else if (title.includes('tool') || title.includes('software') || title.includes('app')) {
            resourceType = 'tool';
          } else if (title.includes('video') || title.includes('watch')) {
            resourceType = 'video';
          }

          return {
            ...resource,
            imageUrl: getResourceIconDataUrl(resourceType)
          };
        }
        return resource;
      });

      // Update cache with the new affiliate resources
      if (cachedResponse) {
        cachedResponse.affiliateResources = affiliateResources;
        cacheResponse(url, cachedResponse);
      }

      return res.json({ affiliateResources, requestId });
    }

    return res.status(400).json({ error: 'Invalid resourceType. Must be "key-takeaways", "tips-links", or "affiliate".' });

  } catch (error) {
    // Enhanced error logging with stack trace and more context
    console.error('Error in /tiptop-resources endpoint:', error);
    console.error('Error stack:', error.stack);
    console.error('Request URL:', url);
    console.error('Resource type:', resourceType);

    // Determine if this is a timeout error
    const isTimeoutError = error.message && (
      error.message.includes('timeout') ||
      error.message.includes('ETIMEDOUT') ||
      error.message.includes('ESOCKETTIMEDOUT') ||
      error.code === 'ECONNABORTED'
    );

    // Provide a more specific error message based on the error type
    let errorMessage = 'Internal server error processing TipTop resources request';
    let statusCode = 500;

    if (isTimeoutError) {
      errorMessage = 'The request timed out while generating resources. The page may be too complex to process quickly.';
      statusCode = 504; // Gateway Timeout
    } else if (error.message && error.message.includes('ECONNREFUSED')) {
      errorMessage = 'Could not connect to the AI service. Please try again later.';
      statusCode = 503; // Service Unavailable
    } else if (error.response && error.response.status) {
      // Handle API-specific errors
      statusCode = error.response.status;
      errorMessage = `AI service returned an error (${statusCode})`;
      if (error.response.data && error.response.data.error) {
        errorMessage += `: ${error.response.data.error}`;
      }
    }

    // Return a more informative error response with appropriate fallback content
    const response = {
      error: errorMessage,
      details: error.message,
      requestId: requestId || Date.now().toString()
    };

    // Add appropriate fallback content based on the resource type
    if (resourceType === 'key-takeaways') {
      // Return an empty array for key takeaways on error
      // This will hide the section in the UI rather than showing generic content
      response.keyTakeaways = [];
    } else if (resourceType === 'tips-links') {
      response.resources = [
        {
          title: "Research Best Practices",
          description: "When exploring this topic further, look for peer-reviewed sources and expert opinions to ensure you're getting accurate information.",
          url: "https://www.google.com/search?q=research+best+practices"
        },
        {
          title: "Compare Multiple Sources",
          description: "Cross-reference information across multiple reliable sources to get a comprehensive understanding of this topic.",
          url: "https://scholar.google.com/"
        }
      ];
    } else if (resourceType === 'affiliate') {
      response.affiliateResources = [
        {
          title: "Recommended Books",
          description: "Find books related to this topic on Amazon.",
          //price: "$15.99+",
          url: "https://www.amazon.com/s?k=books",
          imageUrl: getResourceIconDataUrl('book')
        },
        {
          title: "Online Courses",
          description: "Expand your knowledge with online courses on this subject.",
          //price: "$12.99+",
          url: "https://www.udemy.com/courses/search/?q=learning",
          imageUrl: getResourceIconDataUrl('course')
        }
      ];
    }

    res.status(statusCode).json(response);
  }
});

// Health check endpoint for Kubernetes probes
app.get('/check', (_, res) => {
  // Silent health check - status is logged by morgan middleware
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString()
  });
});

// ACME Challenge endpoint for Let's Encrypt validation
app.get('/.well-known/acme-challenge/:token', (_, res) => {
  res.status(200).send('OK');
});

// Fallback route for undefined paths
app.use((req, res) => {
  // Don't log 404s for known security probe paths
  const isSuspiciousPath = req.path.toLowerCase().includes('/.env') ||
                          req.path.toLowerCase().includes('/docker') ||
                          req.path.toLowerCase().includes('/.git') ||
                          req.path.toLowerCase().includes('/wp-');

  if (!isSuspiciousPath) {
    console.log(`Received ${req.method} request at ${req.originalUrl} - 404`);
  }
  res.status(404).send('Page not found');
});

// Initialize social database tables
socialDb.initSocialTables().catch(err => {
  console.error('Failed to initialize social database tables:', err);
  // Non-fatal error, continue with startup
});

// Mount social API endpoints
app.use('/social', socialApiRouter);

// Add a route to handle WebSocket upgrade requests explicitly
app.get('/ws', (req, res) => {
  console.log('Received GET request to /ws - this should be upgraded to WebSocket');
  res.status(400).send('WebSocket endpoint - connect with a WebSocket client');
});

// Export the Express app as the Cloud Function
functions.http('AIWebMon', app);

// After exporting the function, create an HTTP server for WebSocket support
// This won't affect the functions-framework but will allow WebSockets to work
let server;

// Create a separate HTTP server for WebSocket
server = http.createServer(app);

// WebSocket server is now handled by separate tiptop-websocket service
console.log('Social features configuration:', config.social);
console.log('ENABLE_SOCIAL_FEATURES environment variable:', process.env.ENABLE_SOCIAL_FEATURES);
console.log('WebSocket server is handled by separate tiptop-websocket service');

// This event may not be triggered in the functions-framework environment
app.on('listening', () => {
  const address = app.address();
  if (address) {
    console.log(`App listening on port ${address.port}`);
  }
});
