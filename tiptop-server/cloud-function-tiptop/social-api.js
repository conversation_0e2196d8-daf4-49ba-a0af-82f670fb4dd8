/**
 * API endpoints for TipTop social features
 * This module provides the REST API endpoints for social features.
 */

const express = require('express');
const socialDb = require('./social-db');
const config = require('./config');

// Create a router for social API endpoints
const router = express.Router();

// Middleware to check if social features are enabled
function checkSocialEnabled(req, res, next) {
  if (!config.social.enabled) {
    return res.status(403).json({
      error: 'Social features are disabled',
      code: 'SOCIAL_DISABLED'
    });
  }
  next();
}

// Get active users for a page
router.get('/users', checkSocialEnabled, async (req, res) => {
  try {
    const { url } = req.query;
    
    if (!url) {
      return res.status(400).json({
        error: 'URL parameter is required',
        code: 'MISSING_URL'
      });
    }
    
    const users = await socialDb.getActiveUsers(url);
    
    res.json({
      success: true,
      users: users,
      count: users.length
    });
  } catch (error) {
    console.error('Error getting active users:', error);
    res.status(500).json({
      error: 'Failed to get active users',
      code: 'SERVER_ERROR'
    });
  }
});

// Record user presence on a page
router.post('/presence', checkSocialEnabled, async (req, res) => {
  try {
    const { userId, url, userName } = req.body;
    
    if (!userId || !url) {
      return res.status(400).json({
        error: 'userId and url parameters are required',
        code: 'MISSING_PARAMS'
      });
    }
    
    const success = await socialDb.recordUserPresence(userId, url, userName || 'Anonymous User');
    
    if (success) {
      res.json({
        success: true,
        message: 'User presence recorded'
      });
    } else {
      res.status(500).json({
        error: 'Failed to record user presence',
        code: 'DB_ERROR'
      });
    }
  } catch (error) {
    console.error('Error recording user presence:', error);
    res.status(500).json({
      error: 'Failed to record user presence',
      code: 'SERVER_ERROR'
    });
  }
});

// Record user disconnection
router.post('/disconnect', checkSocialEnabled, async (req, res) => {
  try {
    const { userId, url } = req.body;
    
    if (!userId || !url) {
      return res.status(400).json({
        error: 'userId and url parameters are required',
        code: 'MISSING_PARAMS'
      });
    }
    
    const success = await socialDb.recordUserDisconnection(userId, url);
    
    if (success) {
      res.json({
        success: true,
        message: 'User disconnection recorded'
      });
    } else {
      res.status(500).json({
        error: 'Failed to record user disconnection',
        code: 'DB_ERROR'
      });
    }
  } catch (error) {
    console.error('Error recording user disconnection:', error);
    res.status(500).json({
      error: 'Failed to record user disconnection',
      code: 'SERVER_ERROR'
    });
  }
});

// Get recent chat messages for a page
router.get('/messages', checkSocialEnabled, async (req, res) => {
  try {
    const { url, limit } = req.query;
    
    if (!url) {
      return res.status(400).json({
        error: 'URL parameter is required',
        code: 'MISSING_URL'
      });
    }
    
    const maxLimit = config.social.maxMessagesPerPage;
    const validLimit = limit ? Math.min(parseInt(limit), maxLimit) : maxLimit;
    
    const messages = await socialDb.getRecentMessages(url, validLimit);
    
    res.json({
      success: true,
      messages: messages,
      count: messages.length
    });
  } catch (error) {
    console.error('Error getting recent messages:', error);
    res.status(500).json({
      error: 'Failed to get recent messages',
      code: 'SERVER_ERROR'
    });
  }
});

// Post a new chat message
router.post('/messages', checkSocialEnabled, async (req, res) => {
  try {
    const { userId, url, userName, message } = req.body;
    
    if (!userId || !url || !message) {
      return res.status(400).json({
        error: 'userId, url, and message parameters are required',
        code: 'MISSING_PARAMS'
      });
    }
    
    // Basic content moderation - prevent empty or extremely long messages
    if (message.trim().length === 0) {
      return res.status(400).json({
        error: 'Message cannot be empty',
        code: 'INVALID_MESSAGE'
      });
    }
    
    if (message.length > 1000) {
      return res.status(400).json({
        error: 'Message is too long (maximum 1000 characters)',
        code: 'MESSAGE_TOO_LONG'
      });
    }
    
    const result = await socialDb.storeChatMessage(
      userId, 
      url, 
      userName || 'Anonymous User', 
      message
    );
    
    if (result) {
      res.json({
        success: true,
        messageId: result.id,
        timestamp: result.sent_at
      });
    } else {
      res.status(500).json({
        error: 'Failed to store chat message',
        code: 'DB_ERROR'
      });
    }
  } catch (error) {
    console.error('Error storing chat message:', error);
    res.status(500).json({
      error: 'Failed to store chat message',
      code: 'SERVER_ERROR'
    });
  }
});

// Get social statistics
router.get('/stats', checkSocialEnabled, async (req, res) => {
  try {
    const stats = await socialDb.getSocialStats();
    
    res.json({
      success: true,
      stats: stats
    });
  } catch (error) {
    console.error('Error getting social stats:', error);
    res.status(500).json({
      error: 'Failed to get social statistics',
      code: 'SERVER_ERROR'
    });
  }
});

module.exports = router;
