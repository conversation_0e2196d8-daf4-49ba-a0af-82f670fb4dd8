/**
 * Database functions for TipTop social features
 * This module handles database operations for social features.
 */

const { Pool } = require('pg');
const config = require('./config');

// Create a database connection pool
const pool = new Pool({
  user: process.env.PGUSER,
  host: process.env.PGHOST,
  database: process.env.PGDATABASE,
  password: process.env.PGPASSWORD,
  port: process.env.PGPORT,
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
});

// Initialize the database tables for social features
async function initSocialTables() {
  console.log('Initializing social feature database tables...');
  
  try {
    // Create tables for social features
    await pool.query(`
      -- Table for tracking user presence on pages
      CREATE TABLE IF NOT EXISTS social_presence (
        user_id TEXT NOT NULL,
        page_url TEXT NOT NULL,
        user_name TEXT NOT NULL DEFAULT 'Anonymous User',
        connected_at TIMESTAMP NOT NULL DEFAULT NOW(),
        disconnected_at TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE,
        PRIMARY KEY (user_id, page_url)
      );

      -- Create index for faster lookups by page_url
      CREATE INDEX IF NOT EXISTS idx_social_presence_url ON social_presence(page_url);
      CREATE INDEX IF NOT EXISTS idx_social_presence_active ON social_presence(is_active);

      -- Table for storing chat messages
      CREATE TABLE IF NOT EXISTS social_messages (
        id SERIAL PRIMARY KEY,
        user_id TEXT NOT NULL,
        user_name TEXT NOT NULL,
        page_url TEXT NOT NULL,
        message TEXT NOT NULL,
        sent_at TIMESTAMP NOT NULL DEFAULT NOW()
      );

      -- Create index for faster message retrieval by page_url
      CREATE INDEX IF NOT EXISTS idx_social_messages_url ON social_messages(page_url);
      CREATE INDEX IF NOT EXISTS idx_social_messages_time ON social_messages(sent_at);
    `);
    
    console.log('Social feature database tables initialized successfully');
  } catch (error) {
    console.error('Error initializing social feature database tables:', error);
    throw error;
  }
}

// Record user presence on a page
async function recordUserPresence(userId, pageUrl, userName = 'Anonymous User') {
  try {
    await pool.query(
      `INSERT INTO social_presence (user_id, page_url, user_name, connected_at, is_active)
       VALUES ($1, $2, $3, NOW(), TRUE)
       ON CONFLICT (user_id, page_url) 
       DO UPDATE SET connected_at = NOW(), is_active = TRUE, disconnected_at = NULL`,
      [userId, pageUrl, userName]
    );
    return true;
  } catch (error) {
    console.error('Error recording user presence:', error);
    return false;
  }
}

// Record user disconnection
async function recordUserDisconnection(userId, pageUrl) {
  try {
    await pool.query(
      `UPDATE social_presence 
       SET disconnected_at = NOW(), is_active = FALSE
       WHERE user_id = $1 AND page_url = $2`,
      [userId, pageUrl]
    );
    return true;
  } catch (error) {
    console.error('Error recording user disconnection:', error);
    return false;
  }
}

// Get active users for a page
async function getActiveUsers(pageUrl) {
  try {
    const result = await pool.query(
      `SELECT user_id, user_name, connected_at
       FROM social_presence
       WHERE page_url = $1 AND is_active = TRUE
       ORDER BY connected_at`,
      [pageUrl]
    );
    return result.rows;
  } catch (error) {
    console.error('Error getting active users:', error);
    return [];
  }
}

// Store a chat message
async function storeChatMessage(userId, pageUrl, userName, message) {
  try {
    const result = await pool.query(
      `INSERT INTO social_messages (user_id, page_url, user_name, message, sent_at)
       VALUES ($1, $2, $3, $4, NOW())
       RETURNING id, sent_at`,
      [userId, pageUrl, userName, message]
    );
    return result.rows[0];
  } catch (error) {
    console.error('Error storing chat message:', error);
    return null;
  }
}

// Get recent chat messages for a page
async function getRecentMessages(pageUrl, limit = 50) {
  try {
    const result = await pool.query(
      `SELECT id, user_id, user_name, message, sent_at
       FROM social_messages
       WHERE page_url = $1
       ORDER BY sent_at DESC
       LIMIT $2`,
      [pageUrl, limit]
    );
    return result.rows.reverse(); // Return in chronological order
  } catch (error) {
    console.error('Error getting recent messages:', error);
    return [];
  }
}

// Get social statistics
async function getSocialStats() {
  try {
    // Get active user count
    const activeUsersResult = await pool.query(
      `SELECT COUNT(DISTINCT user_id) as active_users
       FROM social_presence
       WHERE is_active = TRUE`
    );
    
    // Get active page count
    const activePagesResult = await pool.query(
      `SELECT COUNT(DISTINCT page_url) as active_pages
       FROM social_presence
       WHERE is_active = TRUE`
    );
    
    // Get message count in last 24 hours
    const recentMessagesResult = await pool.query(
      `SELECT COUNT(*) as recent_messages
       FROM social_messages
       WHERE sent_at > NOW() - INTERVAL '24 hours'`
    );
    
    // Get top 5 active pages
    const topPagesResult = await pool.query(
      `SELECT page_url, COUNT(DISTINCT user_id) as user_count
       FROM social_presence
       WHERE is_active = TRUE
       GROUP BY page_url
       ORDER BY user_count DESC
       LIMIT 5`
    );
    
    return {
      activeUsers: parseInt(activeUsersResult.rows[0].active_users),
      activePages: parseInt(activePagesResult.rows[0].active_pages),
      recentMessages: parseInt(recentMessagesResult.rows[0].recent_messages),
      topPages: topPagesResult.rows
    };
  } catch (error) {
    console.error('Error getting social stats:', error);
    return {
      activeUsers: 0,
      activePages: 0,
      recentMessages: 0,
      topPages: []
    };
  }
}

module.exports = {
  initSocialTables,
  recordUserPresence,
  recordUserDisconnection,
  getActiveUsers,
  storeChatMessage,
  getRecentMessages,
  getSocialStats
};
