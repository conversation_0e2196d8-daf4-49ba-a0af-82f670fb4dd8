# Grok Model Configuration Guide

This document explains how to configure and update the Grok model used by the TipTop extension.

## Overview

The TipTop extension uses the Groq API to analyze web content and generate summaries, key takeaways, tips, and links. The specific Grok model used for these API calls is now configurable via an environment variable.

## Available Models

The following Grok models are available:

- `grok-3-mini-fast-beta` (fastest, recommended for production)
- `grok-3-mini-beta` (more capable but slower)
- `grok-2-latest` (older model, not recommended)

## How to Update the Model

### 1. Update the Kubernetes Secret

The model is configured via the `GROK_MODEL` environment variable, which is stored in the `cloud-function-secrets` Kubernetes secret.

To update the model, you need to update this secret. Here's how:

```bash
# Get the current secrets
kubectl get secret cloud-function-secrets -n tiptop -o yaml > cloud-function-secrets.yaml

# Edit the file to add or update the GROK_MODEL key
# The value should be base64 encoded

# For grok-3-mini-fast-beta:
echo -n "grok-3-mini-fast-beta" | base64
# Output: Z3Jvay0zLW1pbmktZmFzdC1iZXRh

# For grok-3-mini-beta:
echo -n "grok-3-mini-beta" | base64
# Output: Z3Jvay0zLW1pbmktYmV0YQ==

# Add or update the GROK_MODEL key in the data section of cloud-function-secrets.yaml:
# data:
#   ...
#   GROK_MODEL: Z3Jvay0zLW1pbmktZmFzdC1iZXRh

# Apply the updated secret
kubectl apply -f cloud-function-secrets.yaml
```

### 2. Restart the TipTop Deployment

After updating the secret, you need to restart the TipTop deployment to pick up the new environment variable:

```bash
kubectl rollout restart deployment cloud-function-tiptop-deployment -n tiptop
```

### 3. Verify the Update

You can verify that the deployment is using the new model by checking the logs:

```bash
# Get the pod name
kubectl get pods -n tiptop | grep cloud-function-tiptop

# Check the logs
kubectl logs <pod-name> -n tiptop | grep "Using the model specified in environment variables"
```

## Default Configuration

If the `GROK_MODEL` environment variable is not set, the application will default to using `grok-3-mini-fast-beta`.

## Performance Considerations

- `grok-3-mini-fast-beta` is the fastest model and is recommended for production use to minimize timeouts.
- `grok-3-mini-beta` may provide slightly better quality results but is slower.
- If you experience timeouts with the current model, consider switching to a faster model.

## Rate Limits

Remember that the Groq API has a rate limit of 10 requests per second (rps). The application is designed to work within this limit by:

1. Batching API calls where possible
2. Using parallel processing with controlled concurrency
3. Prioritizing critical features when time is limited

Changing to a different model will not affect these rate limits.
