/**
 * Configuration for the TipTop service
 * This module centralizes all configuration settings.
 */

// Load environment variables
require('dotenv').config();

// Determine if we're in test mode from environment variables
const isTestMode = process.env.TIPTOP_TEST_MODE === 'true' || process.env.NODE_ENV === 'development';
console.log(`Cloud Function running in ${isTestMode ? 'TEST' : 'PRODUCTION'} mode`);

// Get server URL based on mode
const SERVER_URL = process.env.SERVER_URL || 'http://localhost:8080';

// Configuration object with default values that can be overridden by environment variables
const config = {
  // Environment
  isTestMode: isTestMode,
  environment: isTestMode ? 'test' : 'production',
  // Server configuration
  port: process.env.PORT || 8080,
  timeout: parseInt(process.env.TIMEOUT || '15000'), // Default 15 second timeout

  // Grok API configuration
  grokApiUrl: process.env.GROK_API_URL || 'https://api.groq.com/openai/v1/chat/completions',
  grokApiKey: process.env.GROK_API_KEY,
  grokModel: process.env.GROK_MODEL || 'grok-3-mini-fast-beta',

  // Stripe configuration
  stripeSecretKey: process.env.STRIPE_SECRET_KEY,
  stripeWebhookSecret: process.env.STRIPE_WEBHOOK_SECRET,

  // Cache configuration
  enableCache: process.env.ENABLE_CACHE !== 'false', // Enable by default
  cacheTTL: parseInt(process.env.CACHE_TTL || '3600'), // Default 1 hour cache TTL

  // Retry configuration
  maxRetries: parseInt(process.env.MAX_RETRIES || '3'), // Default 3 retries

  // Email configuration
  emailUser: process.env.EMAIL_USER,
  emailPass: process.env.EMAIL_PASS,
  emailHost: process.env.EMAIL_HOST || 'smtp.gmail.com',
  emailPort: parseInt(process.env.EMAIL_PORT || '465'),
  emailFrom: process.env.EMAIL_FROM || 'TipTop <<EMAIL>>',

  // Database configuration
  dbHost: process.env.DB_HOST || 'localhost',
  dbUser: process.env.DB_USER || 'postgres',
  dbPassword: process.env.DB_PASSWORD,
  dbName: process.env.DB_NAME || 'tiptop',
  dbPort: parseInt(process.env.DB_PORT || '5432'),

  // Feature flags
  enableAsyncLoading: process.env.ENABLE_ASYNC_LOADING !== 'false', // Enable by default
};

// Social features configuration
config.social = {
  enabled: process.env.ENABLE_SOCIAL_FEATURES !== 'false', // Enable by default
  maxMessagesPerPage: parseInt(process.env.MAX_MESSAGES_PER_PAGE || '100'),
  messageHistoryHours: parseInt(process.env.MESSAGE_HISTORY_HOURS || '24'),
  maxUsersPerPage: parseInt(process.env.MAX_USERS_PER_PAGE || '50'),
  heartbeatInterval: parseInt(process.env.HEARTBEAT_INTERVAL || '30000'), // 30 seconds
  defaultOptOut: process.env.DEFAULT_OPT_OUT === 'true', // Default to opt-in (false)
};

// Validate essential environment variables
if (!config.stripeSecretKey) {
  console.warn('STRIPE_SECRET_KEY environment variable is not set - payment features will be disabled');
}
if (!config.stripeWebhookSecret) {
  console.warn('STRIPE_WEBHOOK_SECRET environment variable is not set - webhook verification will be disabled');
}
if (!config.grokApiUrl) {
  console.error('GROK_API_URL environment variable is not set');
}
if (!config.grokApiKey) {
  console.error('GROK_API_KEY environment variable is not set');
}

// Validate Grok model configuration
const validGrokModels = ['grok-3-mini-fast-beta', 'grok-3-mini-beta', 'grok-2-latest'];
if (!validGrokModels.includes(config.grokModel)) {
  console.warn(`Warning: GROK_MODEL '${config.grokModel}' is not in the list of known models: ${validGrokModels.join(', ')}. Using it anyway, but this may cause errors.`);
}

// Export the configuration
console.log('Exporting config with social:', config.social);
module.exports = config;
