# TipTop Asynchronous Loading Guide

This document explains how to use the new asynchronous loading feature in the TipTop extension.

## Overview

The TipTop extension now supports asynchronous loading of content to improve the user experience. Instead of waiting for all content to be generated before displaying anything, the extension can now:

1. Display the summary immediately
2. Load the "Key Takeaways", "Tips & Links", and "Related Resources" sections in the background
3. Update the UI as each section becomes available

This approach significantly improves the perceived performance of the extension, as users can start reading the summary while the other sections are still being generated.

## API Changes

### 1. Initial Request

The `/tiptop` endpoint now supports partial responses. When a request is made, it will:

- Always return the summary and key takeaways
- Include flags indicating if additional resources are pending
- Include a unique `requestId` for follow-up requests

Example response:

```json
{
  "summary": {
    "text": "This is a summary of the page...",
    "source": "Grok AI"
  },
  "keyTakeaways": [...],
  "isPartialResponse": true,
  "pendingResources": true,
  "pendingAffiliateResources": true,
  "requestId": "1234567890",
  "responseTime": 5000,
  "fromCache": false
}
```

Note that the `keyTakeaways` are included in the initial response, but they can also be loaded asynchronously if needed.

### 2. Follow-up Requests

To fetch the pending resources, make a POST request to the `/tiptop-resources` endpoint with:

```json
{
  "url": "https://example.com/page",
  "requestId": "1234567890",
  "resourceType": "tips-links" // or "affiliate" or "key-takeaways"
}
```

The response will include the requested resources:

```json
{
  "resources": [...], // For tips-links
  "requestId": "1234567890"
}
```

or

```json
{
  "affiliateResources": [...], // For affiliate
  "requestId": "1234567890"
}
```

or

```json
{
  "keyTakeaways": [...], // For key-takeaways
  "requestId": "1234567890"
}
```

## Implementation Guide for the Extension

Here's how to implement asynchronous loading in the TipTop extension:

1. Make the initial request to `/tiptop` as usual
2. Display the summary immediately
3. Check if `isPartialResponse` is true
4. Display key takeaways if they're included in the initial response
5. If `pendingResources` is true, make a follow-up request to `/tiptop-resources` with `resourceType: "tips-links"`
6. If `pendingAffiliateResources` is true, make a follow-up request to `/tiptop-resources` with `resourceType: "affiliate"`
7. Update the UI as each section's data is received

### Example JavaScript Implementation

```javascript
// Initial request
fetch('/tiptop', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ url: currentUrl })
})
.then(response => response.json())
.then(data => {
  // Display summary immediately
  displaySummary(data.summary);

  // Display key takeaways if available
  if (data.keyTakeaways && data.keyTakeaways.length > 0) {
    displayKeyTakeaways(data.keyTakeaways);
  } else {
    // Show loading indicator for key takeaways
    showLoadingIndicator('key-takeaways');
  }

  // Check if we need to fetch additional resources
  if (data.isPartialResponse) {
    // Fetch Key Takeaways if not included in the initial response
    if (!data.keyTakeaways || data.keyTakeaways.length === 0) {
      fetchKeyTakeaways(currentUrl, data.requestId);
    }

    // Fetch Tips & Links if pending
    if (data.pendingResources) {
      fetchTipsAndLinks(currentUrl, data.requestId);
    } else if (data.resources) {
      displayResources(data.resources);
    }

    // Fetch Related Resources if pending
    if (data.pendingAffiliateResources) {
      fetchAffiliateResources(currentUrl, data.requestId);
    } else if (data.affiliateResources) {
      displayAffiliateResources(data.affiliateResources);
    }
  } else {
    // Display all sections if this is a complete response
    displayResources(data.resources);
    displayAffiliateResources(data.affiliateResources);
  }
});

// Function to fetch Key Takeaways
function fetchKeyTakeaways(url, requestId) {
  fetch('/tiptop-resources', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      url: url,
      requestId: requestId,
      resourceType: 'key-takeaways'
    })
  })
  .then(response => response.json())
  .then(data => {
    // Hide loading indicator and display the key takeaways
    hideLoadingIndicator('key-takeaways');
    displayKeyTakeaways(data.keyTakeaways);
  })
  .catch(error => {
    // Handle error and show fallback content
    hideLoadingIndicator('key-takeaways');
    displayFallbackKeyTakeaways();
  });
}

// Function to fetch Tips & Links
function fetchTipsAndLinks(url, requestId) {
  // Show loading indicator for Tips & Links section
  showLoadingIndicator('tips-links');

  fetch('/tiptop-resources', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      url: url,
      requestId: requestId,
      resourceType: 'tips-links'
    })
  })
  .then(response => response.json())
  .then(data => {
    // Hide loading indicator and display the resources
    hideLoadingIndicator('tips-links');
    displayResources(data.resources);
  })
  .catch(error => {
    // Handle error and show fallback content
    hideLoadingIndicator('tips-links');
    displayFallbackResources();
  });
}

// Function to fetch Related Resources
function fetchAffiliateResources(url, requestId) {
  // Show loading indicator for Related Resources section
  showLoadingIndicator('affiliate');

  fetch('/tiptop-resources', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      url: url,
      requestId: requestId,
      resourceType: 'affiliate'
    })
  })
  .then(response => response.json())
  .then(data => {
    // Hide loading indicator and display the resources
    hideLoadingIndicator('affiliate');
    displayAffiliateResources(data.affiliateResources);
  })
  .catch(error => {
    // Handle error and show fallback content
    hideLoadingIndicator('affiliate');
    displayFallbackAffiliateResources();
  });
}
```

## Benefits

This asynchronous loading approach provides several benefits:

1. **Faster Initial Display**: Users see the summary immediately, improving perceived performance
2. **Progressive Loading**: Content appears as it becomes available, providing a more dynamic experience
3. **Reduced Timeouts**: By splitting the requests, we reduce the chance of timeouts for the entire response
4. **Better Resource Utilization**: The server can process requests more efficiently by focusing on one section at a time
5. **Improved User Experience**: Users can start reading the summary while waiting for other sections to load

## Fallback Behavior

If a follow-up request fails, the extension should display default content for that section. The server will always return at least 2 items for each section, even if they are default items.
