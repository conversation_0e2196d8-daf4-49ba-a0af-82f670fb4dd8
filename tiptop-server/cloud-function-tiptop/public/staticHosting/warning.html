<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Warning - SmartParent</title>
    <style>
        :root {
            --primary-color: #2E5BFF;
            --warning-color: #FF3B30;
            --secondary-color: #2c3e50;
            --text-color: #333333;
            --background-color: #f8f9fa;
            --container-bg: #ffffff;
            --border-radius: 12px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
            --warning-bg: #FFF5F5;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 100%;
            max-width: 600px;
            margin: 20px;
            padding: 40px;
            background: var(--container-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            text-align: center;
        }

        .warning-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: var(--warning-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
            position: relative;
            animation: scaleIn 0.5s ease-out;
        }

        .warning-icon::before {
            content: "⚠";
        }

        h1 {
            color: var(--primary-color);
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .warning-box {
            background-color: var(--warning-bg);
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
            text-align: center;
            border: 2px solid var(--warning-color);
        }

        .warning-box h2 {
            color: var(--warning-color);
            font-size: 2em;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .warning-box p {
            color: var(--warning-color);
            font-size: 1.2em;
            line-height: 1.8;
            margin: 0;
        }

        @keyframes scaleIn {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
                margin: 15px;
            }

            h1 {
                font-size: 2em;
            }

            .warning-box h2 {
                font-size: 1.6em;
            }

            .warning-box p {
                font-size: 1.1em;
            }

            .warning-icon {
                width: 60px;
                height: 60px;
                font-size: 30px;
            }
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --text-color: #e1e1e1;
                --background-color: #1a1a1a;
                --container-bg: #2d2d2d;
                --warning-bg: #3F1D1D;
            }

            .warning-box {
                border-color: var(--warning-color);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="warning-icon"></div>
        <h1>Access Warning</h1>
        <div class="warning-box">
            <h2>Website Blocked</h2>
            <p>This website has been identified as potentially inappropriate or unsafe based on your SmartParent settings.</p>
        </div>
    </div>
</body>
</html>
