<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TipTop Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2980b9;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        h2 {
            color: #3498db;
            margin-top: 30px;
        }
        p {
            margin-bottom: 15px;
        }
        .last-updated {
            font-style: italic;
            color: #7f8c8d;
            margin-bottom: 30px;
        }
        .important {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin-bottom: 20px;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 10px;
        }
        .contact {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 40px;
        }
        .faq-item {
            margin-bottom: 25px;
        }
        .faq-question {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        .faq-answer {
            padding-left: 15px;
            border-left: 3px solid #eee;
        }
    </style>
</head>
<body>
    <h1>TipTop Support</h1>
    <div class="last-updated">Last Updated: May 15, 2025</div>

    <div class="important">
        <p>Thank you for using TipTop! We're here to help you get the most out of your experience with our AI-powered web insights extension.</p>
    </div>

    <h2>Frequently Asked Questions</h2>
    
    <div class="faq-item">
        <div class="faq-question">How does TipTop work?</div>
        <div class="faq-answer">
            <p>TipTop uses advanced AI to analyze the content of webpages you visit. When you click the TipTop button, our AI processes the page content and provides you with a summary, key takeaways, reading time estimates, and relevant tips and resources.</p>
        </div>
    </div>
    
    <div class="faq-item">
        <div class="faq-question">Is my browsing data private?</div>
        <div class="faq-answer">
            <p>Yes! TipTop only processes the content of the current webpage when you click the TipTop button. We don't track your browsing history, store your personal information, or build user profiles. For more details, please see our <a href="privacy.html">Privacy Policy</a>.</p>
        </div>
    </div>
    
    <div class="faq-item">
        <div class="faq-question">Why isn't TipTop working on certain websites?</div>
        <div class="faq-answer">
            <p>TipTop works best on content-rich websites like articles, blogs, and documentation. It may not work as effectively on:</p>
            <ul>
                <li>Websites with minimal text content</li>
                <li>Highly interactive web applications</li>
                <li>Websites that block content scripts</li>
                <li>Secure pages (https://) that contain mixed content</li>
            </ul>
            <p>If you're experiencing issues with a specific website, please contact our support team.</p>
        </div>
    </div>
    
    <div class="faq-item">
        <div class="faq-question">How accurate are the reading time estimates?</div>
        <div class="faq-answer">
            <p>Our reading time estimates are based on an average reading speed of 200 words per minute. Your actual reading time may vary depending on your personal reading speed, the complexity of the content, and how thoroughly you engage with the material.</p>
        </div>
    </div>
    
    <div class="faq-item">
        <div class="faq-question">How can I customize TipTop?</div>
        <div class="faq-answer">
            <p>We're working on customization options for future releases. Stay tuned for features like theme selection, font size adjustment, and personalized content preferences.</p>
        </div>
    </div>

    <div class="contact">
        <h2>Contact Us</h2>
        <p>If you have any questions, feedback, or need assistance, please reach out to our support team:</p>
        <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
        <p>We typically respond within 24-48 hours during business days.</p>
    </div>
</body>
</html>
