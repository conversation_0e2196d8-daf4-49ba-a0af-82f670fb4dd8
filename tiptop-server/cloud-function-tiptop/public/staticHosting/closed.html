<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Unavailable - SmartParent</title>
    <style>
        :root {
            --primary-color: #2E5BFF;
            --info-color: #60A5FA;
            --secondary-color: #2c3e50;
            --text-color: #333333;
            --background-color: #f8f9fa;
            --container-bg: #ffffff;
            --border-radius: 12px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
            --info-bg: #EFF6FF;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 100%;
            max-width: 600px;
            margin: 20px;
            padding: 40px;
            background: var(--container-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            text-align: center;
        }

        .maintenance-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: var(--info-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
            position: relative;
            animation: scaleIn 0.5s ease-out;
        }

        .maintenance-icon::before {
            content: "🔧";
            font-size: 36px;
        }

        h1 {
            color: var(--primary-color);
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .info-box {
            background-color: var(--info-bg);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .estimated-time {
            font-weight: 600;
            color: var(--secondary-color);
            margin: 15px 0;
        }

        p {
            margin-bottom: 20px;
            font-size: 1.1em;
            color: var(--text-color);
            line-height: 1.8;
        }

        .status-link {
            display: inline-block;
            margin-top: 20px;
            padding: 12px 24px;
            background-color: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }

        .status-link:hover {
            background-color: #1a3ccc;
            text-decoration: none;
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        a:hover {
            color: #1a3ccc;
            text-decoration: underline;
        }

        @keyframes scaleIn {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .features-list {
            text-align: left;
            margin: 20px 0;
            padding-left: 20px;
        }

        .features-list li {
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
                margin: 15px;
            }

            h1 {
                font-size: 2em;
            }

            p {
                font-size: 1em;
            }

            .maintenance-icon {
                width: 60px;
                height: 60px;
                font-size: 30px;
            }

            .maintenance-icon::before {
                font-size: 28px;
            }

            .features-list li {
                font-size: 1em;
            }
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --text-color: #e1e1e1;
                --background-color: #1a1a1a;
                --container-bg: #2d2d2d;
                --info-color: #2563EB;
                --info-bg: #1E3A8A;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="maintenance-icon"></div>
        <h1>Service Temporarily Unavailable</h1>
        <div class="info-box">
            <p>SmartParent is currently undergoing scheduled maintenance to improve our services.</p>
            <p class="estimated-time">Estimated completion time: 2 hours</p>
        </div>
        <p>During this maintenance period, we are:</p>
        <ul class="features-list">
            <li>Upgrading our systems for improved performance</li>
            <li>Implementing new security features</li>
            <li>Enhancing our parental control capabilities</li>
        </ul>
        <p>We apologize for any inconvenience. Your service will resume automatically when maintenance is complete.</p>
        <a href="/status" class="status-link">Check Service Status</a>
        <p style="margin-top: 30px;">For urgent matters, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
    </div>
</body>
</html>
