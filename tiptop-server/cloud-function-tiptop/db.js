const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.PGUSER,
  host: process.env.PGHOST,
  database: process.env.PGDATABASE,
  password: process.env.PGPASSWORD,
  port: process.env.PGPORT,
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
});

// Database configuration
const MAX_RETRIES = 5;          // Maximum number of connection retry attempts
const INITIAL_DELAY_MS = 2000;  // Initial delay before first retry, doubles with each attempt

/**
 * Initialize database connection and create installations table
 * Uses exponential backoff for connection retries
 * @returns {Promise<void>}
 * @throws {Error} If database initialization fails after max retries
 */
async function initializeDatabase() {
  let retries = 0;
  
  while (retries < MAX_RETRIES) {
    try {
      // Test connection first
      await pool.query('SELECT 1');
      
      // Create installations table (matches db-init.sql for consistency)
      await pool.query(`
        CREATE TABLE IF NOT EXISTS installations (
          ip_hash TEXT PRIMARY KEY,
          email TEXT NULL, -- Added email column
          status TEXT NOT NULL, -- Removed CHECK constraint, logic handled in index.js
          install_timestamp TIMESTAMP DEFAULT NOW() -- Removed updated_at column
        );

        -- Create indexes
        CREATE INDEX IF NOT EXISTS idx_installations_ip_hash ON installations(ip_hash);
        CREATE INDEX IF NOT EXISTS idx_installations_status ON installations(status);
      `);

      // Removed trigger logic for non-existent updated_at column

      console.log('Installations table initialized successfully');
      return;
    } catch (err) {
      if (err.code === 'ECONNREFUSED' || err.code === 'ETIMEDOUT') {
        const delay = INITIAL_DELAY_MS * Math.pow(2, retries);
        console.error(`Connection failed (attempt ${retries + 1}/${MAX_RETRIES}), retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        retries++;
      } else {
        console.error('Database initialization error:', err);
        throw err;
      }
    }
  }
  throw new Error(`Failed to initialize database after ${MAX_RETRIES} attempts`);
}

// --- Installation Management Functions ---

/**
 * Get installation details by IP hash.
 * @param {string} ip_hash - The hashed IP address.
 * @returns {Promise<object|null>} Installation record or null if not found.
 */
async function getInstallation(ip_hash) {
  try {
    const result = await pool.query(
      'SELECT ip_hash, email, status, install_timestamp FROM installations WHERE ip_hash = $1',
      [ip_hash]
    );
    return result.rows[0] || null;
  } catch (err) {
    console.error('Error getting installation:', err);
    throw err; // Re-throw to be handled by caller
  }
}

/**
 * Add a new installation record.
 * @param {string} ip_hash - The hashed IP address.
 * @param {string} initialStatus - The initial status (e.g., 'TrialPendingEmail').
 * @returns {Promise<object>} The newly created installation record.
 */
async function addInstallation(ip_hash, initialStatus) {
  try {
    const result = await pool.query(
      'INSERT INTO installations (ip_hash, status) VALUES ($1, $2) RETURNING ip_hash, email, status, install_timestamp',
      [ip_hash, initialStatus]
    );
    console.log(`Added installation for ${ip_hash} with status ${initialStatus}`);
    return result.rows[0];
  } catch (err) {
    // Handle potential race condition if insert happens concurrently
    if (err.code === '23505') { // unique_violation
      console.warn(`Installation already exists for ${ip_hash}, fetching existing.`);
      return getInstallation(ip_hash);
    }
    console.error('Error adding installation:', err);
    throw err;
  }
}

/**
 * Update the email for an installation.
 * @param {string} ip_hash - The hashed IP address.
 * @param {string} email - The email address to set.
 * @returns {Promise<object|null>} Updated installation record or null if not found.
 */
async function updateInstallationEmail(ip_hash, email) {
  try {
    const result = await pool.query(
      'UPDATE installations SET email = $1 WHERE ip_hash = $2 RETURNING ip_hash, email, status, install_timestamp',
      [email, ip_hash]
    );
    if (result.rowCount > 0) {
        console.log(`Updated email for ${ip_hash}`);
        return result.rows[0];
    }
    return null; // Indicate record not found or not updated
  } catch (err) {
    console.error('Error updating installation email:', err);
    throw err;
  }
}

/**
 * Update the status for an installation.
 * @param {string} ip_hash - The hashed IP address.
 * @param {string} status - The new status to set.
 * @returns {Promise<object|null>} Updated installation record or null if not found.
 */
async function updateInstallationStatus(ip_hash, status) {
    try {
      const result = await pool.query(
        'UPDATE installations SET status = $1 WHERE ip_hash = $2 RETURNING ip_hash, email, status, install_timestamp',
        [status, ip_hash]
      );
      if (result.rowCount > 0) {
          console.log(`Updated status for ${ip_hash} to ${status}`);
          return result.rows[0];
      }
      return null; // Indicate record not found or not updated
    } catch (err) {
      console.error('Error updating installation status:', err);
      throw err;
  }
}

/**
 * Get installation details by email address.
 * Can return multiple installations if the same email is associated with different IPs.
 * @param {string} email - The email address.
 * @returns {Promise<Array<object>>} Array of installation records or empty array if none found.
 */
async function getInstallationsByEmail(email) {
  if (!email) {
    console.warn('getInstallationsByEmail called with null or empty email.');
    return [];
  }
  try {
    const result = await pool.query(
      'SELECT ip_hash, email, status, install_timestamp FROM installations WHERE email = $1',
      [email]
    );
    return result.rows || [];
  } catch (err) {
    console.error('Error getting installations by email:', err);
    throw err; // Re-throw to be handled by caller
  }
}


module.exports = {
  pool,
  initializeDatabase,
  getInstallation,
  addInstallation,
  updateInstallationEmail,
  updateInstallationStatus,
  getInstallationsByEmail // Export the new function
};
