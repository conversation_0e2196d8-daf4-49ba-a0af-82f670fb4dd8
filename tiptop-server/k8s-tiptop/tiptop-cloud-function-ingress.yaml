apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cloud-function-tiptop-ingress
  namespace: tiptop
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod-tiptop
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/acme-challenge-type: http01
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"
    nginx.ingress.kubernetes.io/cors-allow-origin: "chrome-extension://*, https://tiptop.qubitrhythm.com"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - tiptop.qubitrhythm.com
    # Use a dedicated secret for this subdomain. Cert-manager will create it.
    secretName: tiptop-tls
  rules:
  - host: tiptop.qubitrhythm.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cloud-function-tiptop-service # Route to the new service
            port:
              number: 80 # Port exposed by the ClusterIP service
