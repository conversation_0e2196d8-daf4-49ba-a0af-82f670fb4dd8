apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod-tiptop
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod-tiptop
    solvers:
    - selector:
        dnsZones:
          - "tiptop.qubitrhythm.com"
      dns01:
        cloudDNS:
          project: tiptop
          hostedZoneName: tiptop-zone
          serviceAccountSecretRef:
            name: clouddns-service-account
            key: credentials.json
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: tiptop-tls
  namespace: tiptop
spec:
  secretName: tiptop-tls
  issuerRef:
    name: letsencrypt-prod-tiptop
    kind: ClusterIssuer
  dnsNames:
  - "*.tiptop.qubitrhythm.com"  # Wildcard certificate for all subdomains
  - tiptop.qubitrhythm.com      # Base domain
  usages:
    - server auth
    - client auth
