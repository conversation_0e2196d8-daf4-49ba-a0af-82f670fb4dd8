apiVersion: v1
kind: Secret
metadata:
  name: tiptop-cloud-function-secrets
  namespace: tiptop
type: Opaque
data:
  PLAN_PRICE_IDS_STANDARD: cHJpY2VfMVFRR0N4QVI3VmxVSXJFeFg3TjVJa1dE
  STRIPE_SECRET_KEY: c2tfdGVzdF81MVFQVm1tQVI3VmxVSXJFeE9aWXBuSmxoVmcyU25uTWpPbE5CdUhUNnNaUU5CZHFKZDZLZ3h0UUFCNmRaYW5rZEc2M29nelkyeHJjQVl4ZzVmZVEwQkVJcDAwaHl3RVVCOWw=
  STRIPE_WEBHOOK_SECRET: d2hzZWNfdjJIaVF1dEUzbW1pYlR0OUFObUZqY2s0T3ZkQVd5aGM=
  #GROK_MODEL: Z3Jvay0zLW1pbmktZmFzdA==
  GROK_MODEL: Z3Jvay0zLW1pbmk=
  GROK_API_URL: aHR0cHM6Ly9hcGkueC5haS92MS9jaGF0L2NvbXBsZXRpb25z
  GROK_API_KEY: ****************************************************************************************************************
  FROM_EMAIL: ****************************
  EMAIL_PASSWORD: dHZjc3lsbG90a215eXh4dw==
  SMTP_HOST: c210cC5nbWFpbC5jb20=
  SMTP_PORT: NDY1
  SMTP_SECURE: dHJ1ZQ==
  DATABASE_URL: ********************************************************************************************************************  # *******************************************************************************/tiptop
  SERVER_URL: aHR0cDovL2xvY2FsaG9zdDo4MDgw  # http://localhost:8080
  DB_SSL: "ZmFsc2U="  # echo -n "false" | base64
