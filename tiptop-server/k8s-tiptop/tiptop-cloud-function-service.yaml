apiVersion: v1
kind: Service
metadata:
  name: cloud-function-tiptop-service
  namespace: tiptop
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/websocket-services: "cloud-function-tiptop-service"
spec:
  selector:
    app: cloud-function-tiptop
  ports:
  - port: 80
    targetPort: 8080
    nodePort: 30080
    name: http
    protocol: TCP
  - port: 443
    targetPort: 8080
    nodePort: 30443
    name: https
    protocol: TCP
  - port: 81
    targetPort: 8081
    nodePort: 30081
    name: websocket
    protocol: TCP
  type: NodePort
  # This makes the service accessible on fixed ports:
  # HTTP: http://localhost:30080
  # HTTPS: https://localhost:30443
