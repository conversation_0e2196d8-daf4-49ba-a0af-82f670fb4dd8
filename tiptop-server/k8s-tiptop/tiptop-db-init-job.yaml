apiVersion: batch/v1
kind: Job
metadata:
  name: tiptop-db-init
  namespace: tiptop
spec:
  template:
    metadata:
      namespace: tiptop
    spec:
      initContainers:
      - name: wait-for-postgres
        image: postgres:14.9
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        env:
        - name: PGHOST
          value: tiptop-postgres
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              name: tiptop-postgres-secrets
              key: POSTGRES_USER
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: tiptop-postgres-secrets
              key: POSTGRES_PASSWORD
        command: 
        - sh
        - -c
        - |
          until pg_isready -h tiptop-postgres; do
            echo "Waiting for PostgreSQL to be ready..."
            sleep 2
          done
      containers:
      - name: db-init
        image: postgres:14.9
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        env:
        - name: PGHOST
          value: tiptop-postgres
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              name: tiptop-postgres-secrets
              key: POSTGRES_USER
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: tiptop-postgres-secrets
              key: POSTGRES_PASSWORD
        - name: PGDATABASE
          valueFrom:
            secretKeyRef:
              name: tiptop-postgres-secrets
              key: POSTGRES_DB
        command: 
        - sh
        - -c
        - |
          echo "Starting database initialization..."
          if psql -f /scripts/db-init.sql; then
            echo "Database initialization completed successfully"
            exit 0
          else
            echo "Database initialization failed"
            exit 1
          fi
        volumeMounts:
        - name: db-init-scripts
          mountPath: /scripts
      volumes:
      - name: db-init-scripts
        configMap:
          name: tiptop-db-init-configmap
      restartPolicy: Never
  backoffLimit: 4
