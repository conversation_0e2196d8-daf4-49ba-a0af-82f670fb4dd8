apiVersion: v1
kind: ConfigMap
metadata:
  name: tiptop-db-init-configmap
  namespace: tiptop
data:
  db-init.sql: |
    CREATE TABLE IF NOT EXISTS users (
        email TEXT PRIMARY KEY,
        subscribed BOOLEAN NOT NULL,
        plan TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
    );

    CREATE TABLE IF NOT EXISTS installations (
        ip_hash TEXT PRIMARY KEY,
        status TEXT NOT NULL,
        install_timestamp TIMESTAMP DEFAULT NOW()
    );

    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_installations_ip_hash ON installations(ip_hash);

    -- Social Features Schema

    -- Table for tracking user presence on pages
    CREATE TABLE IF NOT EXISTS social_presence (
      user_id TEXT NOT NULL,
      page_url TEXT NOT NULL,
      user_name TEXT NOT NULL DEFAULT 'Anonymous User',
      connected_at TIMESTAMP NOT NULL DEFAULT NOW(),
      disconnected_at TIMESTAMP,
      is_active BOOLEAN DEFAULT TRUE,
      PRIMARY KEY (user_id, page_url)
    );

    -- Create index for faster lookups by page_url
    CREATE INDEX IF NOT EXISTS idx_social_presence_url ON social_presence(page_url);
    CREATE INDEX IF NOT EXISTS idx_social_presence_active ON social_presence(is_active);

    -- Table for storing chat messages
    CREATE TABLE IF NOT EXISTS social_messages (
      id SERIAL PRIMARY KEY,
      user_id TEXT NOT NULL,
      user_name TEXT NOT NULL,
      page_url TEXT NOT NULL,
      message TEXT NOT NULL,
      sent_at TIMESTAMP NOT NULL DEFAULT NOW()
    );

    -- Create index for faster message retrieval by page_url
    CREATE INDEX IF NOT EXISTS idx_social_messages_url ON social_messages(page_url);
    CREATE INDEX IF NOT EXISTS idx_social_messages_time ON social_messages(sent_at);

    -- Table for storing user profiles
    CREATE TABLE IF NOT EXISTS social_users (
      user_id TEXT PRIMARY KEY,
      user_name TEXT NOT NULL DEFAULT 'Anonymous User',
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      last_active_at TIMESTAMP NOT NULL DEFAULT NOW(),
      preferences JSONB DEFAULT '{}'::jsonb
    );

    -- Create index for faster user lookup
    CREATE INDEX IF NOT EXISTS idx_social_users_name ON social_users(user_name);
    CREATE INDEX IF NOT EXISTS idx_social_users_active ON social_users(last_active_at);
