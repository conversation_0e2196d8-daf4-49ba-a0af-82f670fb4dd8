<!DOCTYPE html>
<html>
<head>
    <title>TipTop Lightbulb Button Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        
        .button-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        
        .test-button {
            width: 52px;
            height: 52px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4), 0 0 0 2px rgba(52, 152, 219, 0.2);
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background-color: #2980b9;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 6px 16px rgba(52, 152, 219, 0.5), 0 0 0 4px rgba(52, 152, 219, 0.2);
        }
    </style>
</head>
<body>
    <div class="button-container">
        <h2>TipTop Lightbulb Button</h2>
        <button class="test-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 100 100" fill="none" stroke="white" stroke-width="2">
                <!-- Light rays with proper spacing from bulb -->
                <g class="rays" stroke="white" stroke-width="2.5" stroke-linecap="round">
                    <!-- Top rays -->
                    <line x1="50" y1="5" x2="50" y2="15" opacity="0.8">
                        <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite" />
                    </line>
                    
                    <!-- Top right rays -->
                    <line x1="67" y1="10" x2="62" y2="20" opacity="0.7">
                        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.3s" repeatCount="indefinite" />
                    </line>
                    <line x1="82" y1="20" x2="72" y2="28" opacity="0.8">
                        <animate attributeName="opacity" values="0.6;1;0.6" dur="1.7s" repeatCount="indefinite" />
                    </line>
                    <line x1="92" y1="35" x2="78" y2="40" opacity="0.7">
                        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.1s" repeatCount="indefinite" />
                    </line>
                    
                    <!-- Right ray -->
                    <line x1="95" y1="50" x2="80" y2="50" opacity="0.8">
                        <animate attributeName="opacity" values="0.6;1;0.6" dur="1.9s" repeatCount="indefinite" />
                    </line>
                    
                    <!-- Bottom right rays -->
                    <line x1="90" y1="65" x2="78" y2="60" opacity="0.7">
                        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.2s" repeatCount="indefinite" />
                    </line>
                    
                    <!-- Top left rays -->
                    <line x1="33" y1="10" x2="38" y2="20" opacity="0.7">
                        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="1.8s" repeatCount="indefinite" />
                    </line>
                    <line x1="18" y1="20" x2="28" y2="28" opacity="0.8">
                        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.4s" repeatCount="indefinite" />
                    </line>
                    <line x1="8" y1="35" x2="22" y2="40" opacity="0.7">
                        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2s" repeatCount="indefinite" />
                    </line>
                    
                    <!-- Left ray -->
                    <line x1="5" y1="50" x2="20" y2="50" opacity="0.8">
                        <animate attributeName="opacity" values="0.6;1;0.6" dur="1.6s" repeatCount="indefinite" />
                    </line>
                    
                    <!-- Bottom left ray -->
                    <line x1="10" y1="65" x2="22" y2="60" opacity="0.7">
                        <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.2s" repeatCount="indefinite" />
                    </line>
                </g>
                
                <!-- Main bulb body with glow effect -->
                <path d="M35 50C35 36.2 46.2 25 60 25C73.8 25 85 36.2 85 50C85 58.8 80.6 66.5 73.8 70.8C72.1 72 70.8 73.8 70.8 75.8V80H49.2V75.8C49.2 73.8 47.9 72 46.2 70.8C39.4 66.5 35 58.8 35 50Z" 
                      fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2.5" />
                
                <!-- Filament/brain pattern inside bulb -->
                <path d="M45 45C45 45 50 35 60 35C70 35 75 45 75 45" stroke="white" stroke-width="1.5" opacity="0.9" fill="none">
                    <animate attributeName="opacity" values="0.7;1;0.7" dur="3s" repeatCount="indefinite" />
                </path>
                <path d="M45 55C45 55 50 45 60 45C70 45 75 55 75 55" stroke="white" stroke-width="1.5" opacity="0.8" fill="none">
                    <animate attributeName="opacity" values="0.6;0.9;0.6" dur="2.5s" repeatCount="indefinite" />
                </path>
                
                <!-- Bulb base -->
                <path d="M49.2 80H70.8" stroke="white" stroke-width="2.5" />
                <path d="M52.5 85H67.5" stroke="white" stroke-width="2.5" />
                <path d="M55.8 90H64.2" stroke="white" stroke-width="2.5" />
            </svg>
        </button>
        <p>Click to test hover effect</p>
    </div>
</body>
</html>
