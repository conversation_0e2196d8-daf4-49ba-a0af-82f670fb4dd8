<!DOCTYPE html>
<html>
<head>
    <title>TipTop Image-Only <PERSON><PERSON> Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        
        .button-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        
        .test-button {
            width: 52px;
            height: 52px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            border: none;
            cursor: pointer;
            padding: 0;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-3px) scale(1.05);
        }
        
        .test-button:hover img {
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3)) brightness(1.1);
        }
        
        .test-button img {
            width: 52px;
            height: 52px;
            object-fit: contain;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="button-container">
        <h2>TipTop Image-Only Button</h2>
        <button class="test-button">
            <img src="images/tiptop0.png" alt="TipTop Lightbulb">
        </button>
        <p>Hover to see effect</p>
    </div>
</body>
</html>
