<!DOCTYPE html>
<html>
<head>
    <title>TipTop Image Button Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }

        .button-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .test-button {
            width: 52px;
            height: 52px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4), 0 0 0 2px rgba(52, 152, 219, 0.2);
            transition: all 0.3s ease;
            padding: 0;
            overflow: hidden;
        }

        .test-button:hover {
            background-color: #2980b9;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 6px 16px rgba(52, 152, 219, 0.5), 0 0 0 4px rgba(52, 152, 219, 0.2);
        }

        .test-button img {
            width: 32px;
            height: 32px;
            object-fit: contain;
            filter: brightness(1.2);
        }
    </style>
</head>
<body>
    <div class="button-container">
        <h2>TipTop Image Button</h2>
        <button class="test-button">
            <img src="images/tiptop0.png" alt="TipTop Lightbulb">
        </button>
        <p>Click to test hover effect</p>
    </div>
</body>
</html>
