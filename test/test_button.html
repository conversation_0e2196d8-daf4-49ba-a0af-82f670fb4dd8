<!DOCTYPE html>
<html>
<head>
    <title>TipTop Button Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }

        .button-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .test-button {
            width: 52px;
            height: 52px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4), 0 0 0 2px rgba(52, 152, 219, 0.2);
            transition: all 0.3s ease;
        }

        .test-button:hover {
            background-color: #2980b9;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 6px 16px rgba(52, 152, 219, 0.5), 0 0 0 4px rgba(52, 152, 219, 0.2);
        }
    </style>
</head>
<body>
    <div class="button-container">
        <h2>TipTop Lightbulb Button</h2>
        <button class="test-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <!-- Lightbulb based on tiptop0.png image -->
                <!-- Main bulb body with slight glow -->
                <path d="M12 2.5C8.5 2.5 5.5 5.5 5.5 9c0 2.5 1.5 4.5 3 5.5V17h7v-2.5c1.5-1 3-3 3-5.5 0-3.5-3-6.5-6.5-6.5z" stroke="white" stroke-width="2" fill="rgba(255,255,255,0.4)"></path>

                <!-- Bulb base -->
                <path d="M8.5 17h7" stroke="white" stroke-width="2"></path>
                <path d="M9.5 19h5" stroke="white" stroke-width="2"></path>
                <path d="M10.5 21h3" stroke="white" stroke-width="2"></path>

                <!-- Brain/circuit pattern inside bulb -->
                <path d="M9 8.5C9 8.5 10 7 12 7s3 1.5 3 1.5" stroke="white" stroke-width="1.2" fill="none">
                    <animate attributeName="opacity" values="0.7;1;0.7" dur="3s" repeatCount="indefinite" />
                </path>
                <path d="M9 10.5C9 10.5 10 9 12 9s3 1.5 3 1.5" stroke="white" stroke-width="1.2" fill="none">
                    <animate attributeName="opacity" values="0.8;1;0.8" dur="2.5s" repeatCount="indefinite" />
                </path>

                <!-- Shine/ray lines with clear gaps between bulb and rays -->
                <!-- Each ray has a distinct gap from the bulb to avoid looking like hairs -->

                <!-- Top ray -->
                <line x1="12" y1="-2" x2="12" y2="0" stroke="white" stroke-width="1.5">
                    <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" />
                </line>

                <!-- Top-right rays -->
                <line x1="17" y1="-1" x2="15" y2="1" stroke="white" stroke-width="1.5">
                    <animate attributeName="opacity" values="0.6;0.9;0.6" dur="2.3s" repeatCount="indefinite" />
                </line>
                <line x1="20" y1="1" x2="17" y2="3" stroke="white" stroke-width="1.5">
                    <animate attributeName="opacity" values="0.7;1;0.7" dur="1.7s" repeatCount="indefinite" />
                </line>
                <line x1="22" y1="4" x2="18" y2="6" stroke="white" stroke-width="1.5">
                    <animate attributeName="opacity" values="0.6;0.9;0.6" dur="2.1s" repeatCount="indefinite" />
                </line>

                <!-- Right ray -->
                <line x1="24" y1="9" x2="19" y2="9" stroke="white" stroke-width="1.5">
                    <animate attributeName="opacity" values="0.7;1;0.7" dur="1.9s" repeatCount="indefinite" />
                </line>

                <!-- Bottom-right rays -->
                <line x1="22" y1="14" x2="18" y2="12" stroke="white" stroke-width="1.5">
                    <animate attributeName="opacity" values="0.6;0.9;0.6" dur="2.2s" repeatCount="indefinite" />
                </line>

                <!-- Top-left rays -->
                <line x1="7" y1="-1" x2="9" y2="1" stroke="white" stroke-width="1.5">
                    <animate attributeName="opacity" values="0.7;1;0.7" dur="1.8s" repeatCount="indefinite" />
                </line>
                <line x1="4" y1="1" x2="7" y2="3" stroke="white" stroke-width="1.5">
                    <animate attributeName="opacity" values="0.6;0.9;0.6" dur="2.4s" repeatCount="indefinite" />
                </line>
                <line x1="2" y1="4" x2="6" y2="6" stroke="white" stroke-width="1.5">
                    <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" />
                </line>

                <!-- Left ray -->
                <line x1="0" y1="9" x2="5" y2="9" stroke="white" stroke-width="1.5">
                    <animate attributeName="opacity" values="0.6;0.9;0.6" dur="1.6s" repeatCount="indefinite" />
                </line>

                <!-- Bottom-left rays -->
                <line x1="2" y1="14" x2="6" y2="12" stroke="white" stroke-width="1.5">
                    <animate attributeName="opacity" values="0.7;1;0.7" dur="2.2s" repeatCount="indefinite" />
                </line>
            </svg>
        </button>
        <p>Click to test hover effect</p>
    </div>
</body>
</html>
