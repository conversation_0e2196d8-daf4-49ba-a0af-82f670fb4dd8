# TipTop Extension Permission Justifications

## Storage Permission
The extension uses the 'storage' permission to save user preferences and settings locally. This ensures that user customizations and interaction history are preserved across browsing sessions, providing a consistent and personalized experience without requiring a server-side account.

## Tabs Permission
The 'tabs' permission is necessary for TipTop to access information about the current tab, including its URL and title. This information is essential for generating relevant AI-powered summaries, tips, and recommendations that are specific to the webpage the user is viewing.

## Scripting Permission
The 'scripting' permission allows TipTop to inject and execute the necessary scripts to create the floating button and insights panel. This permission is crucial for the extension to analyze page content and display the AI-generated insights directly within the webpage without redirecting users elsewhere.

## Host Permissions ("<all_urls>")
TipTop requires host permissions to access and analyze the content of webpages that users visit. This is essential for generating accurate summaries, reading time estimates, and contextual recommendations. The extension needs to read the DOM of any webpage to extract meaningful content for AI analysis.

## Remote Code Usage
TipTop connects to https://tiptop.qubitrhythm.com to utilize our AI services for content analysis and summarization. This server hosts the sophisticated AI models that power the extension's insights capabilities. Keeping these models server-side allows us to:

1. Provide more powerful AI analysis than would be possible within browser limitations
2. Regularly update and improve our AI models without requiring extension updates
3. Reduce the extension's resource consumption on users' devices
4. Process complex webpage content more efficiently

All communication with our server is done securely over HTTPS, and we do not store browsing history or personally identifiable information. The server processes webpage content only for the purpose of generating insights and recommendations.
