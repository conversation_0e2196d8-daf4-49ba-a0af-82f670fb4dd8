<!DOCTYPE html>
<html>
<head>
    <title>Test Mode Indicator File Access</title>
</head>
<body>
    <h1>Test Mode Indicator File Access</h1>
    <div id="results"></div>
    
    <script>
        async function testModeIndicator() {
            const results = document.getElementById('results');
            
            try {
                // Test accessing the mode indicator file from extension context
                const response = await fetch('./tiptop-extension/tiptop-mode.json');
                if (response.ok) {
                    const modeData = await response.json();
                    results.innerHTML += `<p><strong>✅ Mode Indicator File:</strong> ${JSON.stringify(modeData, null, 2)}</p>`;
                } else {
                    results.innerHTML += `<p><strong>❌ Mode Indicator File:</strong> Not accessible (${response.status})</p>`;
                }
            } catch (error) {
                results.innerHTML += `<p><strong>❌ Mode Indicator File:</strong> Error - ${error.message}</p>`;
            }
            
            // Test API connectivity
            try {
                const apiResponse = await fetch('http://localhost:30080/tiptop', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ url: 'https://example.com' })
                });
                
                if (apiResponse.ok) {
                    const data = await apiResponse.json();
                    results.innerHTML += `<p><strong>✅ API Test:</strong> Success - Got response with ${data.summary?.text?.length || 0} characters</p>`;
                } else {
                    results.innerHTML += `<p><strong>❌ API Test:</strong> Failed - ${apiResponse.status} ${apiResponse.statusText}</p>`;
                }
            } catch (error) {
                results.innerHTML += `<p><strong>❌ API Test:</strong> Error - ${error.message}</p>`;
            }
        }
        
        testModeIndicator();
    </script>
</body>
</html>
