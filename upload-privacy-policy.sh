#!/bin/bash

# <PERSON><PERSON>t to upload the TipTop privacy policy to the server
# Make sure to set up SSH key authentication for this to work without password prompts

# Configuration
SERVER_USER="your-username"
SERVER_HOST="your-server-hostname"
SERVER_PATH="/path/to/staticHosting"
LOCAL_FILE="tiptop-extension/privacy-policy.html"
REMOTE_FILE="privacy.html"

# Check if the local file exists
if [ ! -f "$LOCAL_FILE" ]; then
  echo "Error: Local file '$LOCAL_FILE' not found."
  exit 1
fi

# Upload the file
echo "Uploading privacy policy to server..."
scp "$LOCAL_FILE" "$SERVER_USER@$SERVER_HOST:$SERVER_PATH/$REMOTE_FILE"

# Check if the upload was successful
if [ $? -eq 0 ]; then
  echo "Privacy policy uploaded successfully to $SERVER_HOST:$SERVER_PATH/$REMOTE_FILE"
  echo "Privacy policy URL: https://tiptop.qubitrhythm.com/staticHosting/privacy.html"
else
  echo "Error: Failed to upload privacy policy."
  exit 1
fi

echo "Done!"
